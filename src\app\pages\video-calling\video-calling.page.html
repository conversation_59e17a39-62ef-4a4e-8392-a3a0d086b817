<!-- Video Calling Page -->
<ion-content [fullscreen]="true" class="video-call-content">

  <!-- Header with back button and doctor name -->
  <div class="video-header">
    <ion-button fill="clear" class="back-button" (click)="goBack()">
      <ion-icon name="chevron-back"></ion-icon>
    </ion-button>
    <div class="doctor-info">
      <span class="doctor-name">Dr<PERSON></span>
      <div class="connection-indicator" [class.connected]="callState().isConnected"></div>
    </div>
    <ion-button fill="clear" class="camera-switch-button" (click)="switchCamera()">
      <ion-icon name="camera-outline"></ion-icon>
    </ion-button>
  </div>

  <!-- Main Video Container -->
  <div class="main-video-container">
    <!-- Remote Video Stream (Doctor) -->
    <div #remoteVideoContainer class="remote-video-container">
      <!-- Placeholder doctor image when not connected -->
      <div class="doctor-placeholder" *ngIf="!callState().isConnected">
        <div class="doctor-image"></div>
      </div>
    </div>

    <!-- Local Video Stream (Patient - Small window) -->
    <div #localVideoContainer
      class="local-video-container"
      [style.top.px]="localPosition().top"
      [style.right.px]="localPosition().right"
      (mousedown)="handleDragStart($event)"
      (touchstart)="handleDragStart($event)">
      <!-- Placeholder patient image when not connected -->
      <div class="patient-placeholder" *ngIf="!callState().isConnected">
        <div class="patient-image"></div>
      </div>
    </div>


  </div>

  <!-- Call Controls Footer -->
  <div class="call-controls-container">
    <div class="call-controls">
      <!-- Camera Toggle -->
      <ion-button
        fill="clear"
        class="control-button"
        [class.active]="callState().isCameraOn"
        (click)="toggleCamera()">
        <ion-icon name="videocam-outline" slot="icon-only"></ion-icon>
      </ion-button>

      <!-- Speaker Toggle -->
      <ion-button
        fill="clear"
        class="control-button"
        [class.active]="callState().isSpeakerOn"
        (click)="toggleSpeaker()">
        <ion-icon name="volume-high-outline" slot="icon-only"></ion-icon>
      </ion-button>

      <!-- Mic Toggle -->
      <ion-button
        fill="clear"
        class="control-button"
        [class.active]="callState().isMicOn"
        (click)="toggleMic()">
        <ion-icon name="mic-outline" slot="icon-only"></ion-icon>
      </ion-button>

      <!-- End Call -->
      <ion-button
        fill="solid"
        class="end-call-button"
        (click)="disconnectCall()">
        <ion-icon name="call-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </div>
  </div>
</ion-content>