<!-- Video Calling Page -->
<ion-content [fullscreen]="true" class="video-call-content">

  <!-- Header with back button and doctor name -->
  <div class="video-header">
    <ion-button fill="clear" class="back-button" (click)="goBack()">
      <ion-icon name="chevron-back"></ion-icon>
    </ion-button>
    <div class="doctor-info">
      <span class="doctor-name">Dr<PERSON></span>
      <div class="connection-indicator" [class.connected]="callState().isConnected"></div>
    </div>
    <ion-button fill="clear" class="camera-switch-button" (click)="switchCamera()">
      <img src="assets/images/camera-rotate.svg" alt="Switch Camera" class="camera-rotate-icon">
    </ion-button>
  </div>

  <!-- Connection Container (when not connected) -->
  <div class="connection-container" *ngIf="!callState().isConnected">
    <div class="connection-content">
      <div class="connection-animation">
        <ion-spinner name="crescent" color="light"></ion-spinner>
      </div>
      <h2 class="connection-title">
        {{ callState().isConnecting ? 'Connecting to consultation...' : 'Preparing video call...' }}
      </h2>
      <p class="connection-subtitle">
        {{ callState().isConnecting ? 'Please wait while we connect you with your doctor' : 'Setting up your consultation session' }}
      </p>

      <!-- Manual join buttons (hidden by default since auto-joining) -->
      <div class="manual-join-buttons" style="display: none;">
        <ion-button
          expand="block"
          class="join-button patient-button"
          (click)="getCallInfo('PATIENT')"
          [disabled]="callState().isConnecting">
          <ion-spinner name="crescent" *ngIf="callState().isConnecting"></ion-spinner>
          {{ callState().isConnecting ? 'Connecting...' : 'Join As Patient' }}
        </ion-button>
        <ion-button
          expand="block"
          class="join-button doctor-button"
          (click)="getCallInfo('DOCTOR')"
          [disabled]="callState().isConnecting">
          <ion-spinner name="crescent" *ngIf="callState().isConnecting"></ion-spinner>
          {{ callState().isConnecting ? 'Connecting...' : 'Join As Doctor' }}
        </ion-button>
      </div>
    </div>
  </div>

  <!-- Main Video Container (when connected) -->
  <div class="main-video-container" *ngIf="callState().isConnected">
    <!-- Remote Video Stream (Doctor) -->
    <div #remoteVideoContainer class="remote-video-container"></div>

    <!-- Local Video Stream (Patient - Small window) -->
    <div #localVideoContainer
      class="local-video-container"
      [style.top.px]="localPosition().top"
      [style.right.px]="localPosition().right"
      (mousedown)="handleDragStart($event)"
      (touchstart)="handleDragStart($event)">
    </div>
  </div>

  <!-- Call Controls Footer -->
  <div class="call-controls-container">
    <div class="call-controls">
      <!-- Camera Toggle -->
      <ion-button
        fill="clear"
        class="control-button"
        [class.active]="callState().isCameraOn"
        (click)="toggleCamera()">
        <ion-icon name="videocam-outline" slot="icon-only"></ion-icon>
      </ion-button>

      <!-- Speaker Selection -->
      <ion-button
        fill="clear"
        class="control-button"
        [class.active]="callState().isSpeakerOn"
        (click)="openSpeakerModal()">
        <ion-icon name="volume-high-outline" slot="icon-only"></ion-icon>
      </ion-button>

      <!-- Mic Toggle -->
      <ion-button
        fill="clear"
        class="control-button"
        [class.active]="callState().isMicOn"
        (click)="toggleMic()">
        <ion-icon name="mic-outline" slot="icon-only"></ion-icon>
      </ion-button>

      <!-- End Call -->
      <ion-button
        fill="solid"
        class="end-call-button"
        (click)="disconnectCall()">
        <ion-icon name="call-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </div>
  </div>
</ion-content>

<!-- Speaker Selection Modal -->
<ion-modal
  #speakerModal
  [isOpen]="showSpeakerModal()"
  (didDismiss)="closeSpeakerModal()"
  class="speaker-modal-class"
  [breakpoints]="[0, 0.4]"
  [initialBreakpoint]="0.4"
  [backdropDismiss]="true">
  <ng-template>
    <div class="speaker-modal">
      <div class="modal-header">
        <h2>Select Speaker</h2>
        <ion-button fill="clear" (click)="closeSpeakerModal()" class="close-button">
          <ion-icon name="close" slot="icon-only"></ion-icon>
        </ion-button>
      </div>

      <div class="speaker-options">
        <!-- Available Speaker Devices Only -->
        <div class="speaker-option"
             *ngFor="let speaker of speakers()"
             [class.selected]="selectedSpeakerId() === speaker.id"
             (click)="selectSpeakerDevice(speaker.id)">
          <div class="speaker-icon">
            <ion-icon name="volume-medium-outline"></ion-icon>
          </div>
          <div class="speaker-info">
            <div class="speaker-name">{{ speaker.name }}</div>
          </div>
          <div class="speaker-check" *ngIf="selectedSpeakerId() === speaker.id">
            <ion-icon name="checkmark" color="primary"></ion-icon>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>