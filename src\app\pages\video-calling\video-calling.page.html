<!-- Video Calling Page -->
<ion-header class="video-call-header">
  <ion-toolbar class="video-call-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()">
        <ion-icon name="chevron-back" color="light"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="video-call-title">Dr<PERSON></ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="switchCamera()">
        <ion-icon name="camera-reverse" color="light"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="video-call-content">
  <!-- Connection Form (Visible when not connected) -->
  <div class="connection-container" *ngIf="!callState().isConnected">
    <div class="connection-content">
      <div class="connection-animation">
        <ion-spinner name="crescent" color="light"></ion-spinner>
      </div>
      <h2 class="connection-title">
        {{ callState().isConnecting ? 'Connecting to consultation...' : 'Preparing video call...' }}
      </h2>
      <p class="connection-subtitle">
        {{ callState().isConnecting ? 'Please wait while we connect you with your doctor' : 'Setting up your consultation session' }}
      </p>

      <!-- Manual join buttons (hidden by default since auto-joining) -->
      <div class="manual-join-buttons" style="display: none;">
        <ion-button
          expand="block"
          class="join-button patient-button"
          (click)="getCallInfo('PATIENT')"
          [disabled]="callState().isConnecting">
          <ion-spinner name="crescent" *ngIf="callState().isConnecting"></ion-spinner>
          {{ callState().isConnecting ? 'Connecting...' : 'Join As Patient' }}
        </ion-button>
        <ion-button
          expand="block"
          class="join-button doctor-button"
          (click)="getCallInfo('DOCTOR')"
          [disabled]="callState().isConnecting">
          <ion-spinner name="crescent" *ngIf="callState().isConnecting"></ion-spinner>
          {{ callState().isConnecting ? 'Connecting...' : 'Join As Doctor' }}
        </ion-button>
      </div>
    </div>
  </div>

  <!-- Video Call UI (Visible when connected) -->
  <div class="video-call-container" *ngIf="callState().isConnected">
    <!-- Remote Video Stream -->
    <div #remoteVideoContainer class="remote-video-container"></div>

    <!-- Local Video Stream (Draggable) -->
    <div #localVideoContainer
      class="local-video-container"
      [style.width.px]="86"
      [style.height.px]="130"
      [style.top.px]="localPosition().top"
      [style.left.px]="localPosition().left"
      (mousedown)="handleDragStart($event)"
      (touchstart)="handleDragStart($event)">
    </div>

    <!-- Connection Status -->
    <div class="connection-status">
      <div class="status-indicator connected"></div>
      <span class="status-text">Connected</span>
    </div>

    <!-- Device Settings Panel -->
    <div class="device-settings-panel" *ngIf="showDeviceSettings()">
      <div class="settings-content">
        <h3 class="settings-title">Speaker Settings</h3>
        <div class="settings-section">
          <label class="settings-label">Select a speaker</label>
          <div class="speaker-list">
            <ion-radio-group [value]="selectedSpeakerId()">
              <ion-item *ngFor="let speaker of speakers()" class="speaker-item">
                <ion-radio
                  slot="start"
                  [value]="speaker.id"
                  (ionSelect)="changeSpeaker(speaker.id)">
                </ion-radio>
                <ion-label class="speaker-label">{{ speaker.name }}</ion-label>
              </ion-item>
            </ion-radio-group>
            <div *ngIf="speakers().length === 0" class="no-speakers">No speakers found</div>
          </div>
          <ion-item class="speaker-toggle">
            <ion-checkbox
              [checked]="callState().isSpeakerOn"
              (ionChange)="toggleSpeaker()">
            </ion-checkbox>
            <ion-label class="toggle-label">
              {{ callState().isSpeakerOn ? "Sound enabled" : "Sound disabled (muted)" }}
            </ion-label>
          </ion-item>
        </div>
        <ion-button
          expand="block"
          fill="clear"
          class="close-settings-button"
          (click)="toggleDeviceSettings()">
          Close
        </ion-button>
      </div>
    </div>

    <!-- Call Controls Footer -->
    <div class="call-controls-container">
      <div class="call-controls">
        <!-- Camera Toggle -->
        <ion-button
          fill="clear"
          class="control-button"
          [class.active]="callState().isCameraOn"
          (click)="toggleCamera()">
          <ion-icon
            [name]="callState().isCameraOn ? 'videocam' : 'videocam-off'"
            slot="icon-only">
          </ion-icon>
        </ion-button>

        <!-- Mic Toggle -->
        <ion-button
          fill="clear"
          class="control-button"
          [class.active]="callState().isMicOn"
          (click)="toggleMic()">
          <ion-icon
            [name]="callState().isMicOn ? 'mic' : 'mic-off'"
            slot="icon-only">
          </ion-icon>
        </ion-button>

        <!-- Speaker Toggle -->
        <ion-button
          fill="clear"
          class="control-button"
          [class.active]="callState().isSpeakerOn"
          (click)="openSpeakerSelection()">
          <ion-icon name="volume-high" slot="icon-only"></ion-icon>
        </ion-button>

        <!-- End Call -->
        <ion-button
          fill="solid"
          color="danger"
          class="end-call-button"
          (click)="disconnectCall()">
          <ion-icon name="call" slot="icon-only"></ion-icon>
        </ion-button>
      </div>
    </div>
  </div>
</ion-content>