<!-- Main Component Container -->
<div class="relative w-full h-screen bg-white overflow-hidden touch-none">

  <!-- Connection Form (Visible when not connected) -->
  <ng-container *ngIf="!callState().isConnected">
    <div class="p-4 max-w-lg mx-auto space-y-12 pt-10">
      <h2 class="text-2xl font-bold text-center text-gray-800">Join a Call</h2>
      <div class="flex flex-col gap-4">
        <button
          class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 w-full disabled:opacity-50"
          (click)="getCallInfo('PATIENT')" [disabled]="callState().isConnecting">
          {{ callState().isConnecting ? 'Connecting...' : 'Join <PERSON> Patient' }}
        </button>
        <button
          class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 w-full disabled:opacity-50"
          (click)="getCallInfo('DOCTOR')" [disabled]="callState().isConnecting">
          {{ callState().isConnecting ? 'Connecting...' : 'Join As Doctor' }}
        </button>
      </div>
    </div>
  </ng-container>

  <!-- Video Call UI (Visible when connected) -->
  <ng-container *ngIf="callState().isConnected">
    <!-- Header -->
    <div class="bg-[#031d3b] text-white flex items-center px-4 py-2 absolute left-0 top-0 w-full z-30">
      <button class="p-2 rounded hover:bg-blue-800">
        <i class="fas fa-chevron-left text-xl relative"></i>
      </button>
      <div class="flex-1 text-left font-semibold text-lg ml-2">Dr. Muneeb Bhatti</div>
      <button class="p-2 rounded hover:bg-blue-800" (click)="switchCamera()">
        <i class="fas fa-camera-rotate text-xl relative"></i>
      </button>
    </div>

    <!-- Remote Video Stream -->
    <div #remoteVideoContainer class="remoteVideo absolute top-0 left-0 w-full h-full z-0 bg-black"></div>

    <!-- Local Video Stream (Draggable) -->
    <div #localVideoContainer
      class="localVideo absolute z-20 rounded-md bg-gray-800 touch-none shadow-lg overflow-hidden" [style.width.px]="86"
      [style.height.px]="130" [style.top.px]="localPosition().top" [style.left.px]="localPosition().left"
      [style.cursor]="'grab'" (mousedown)="handleDragStart($event)" (touchstart)="handleDragStart($event)">
    </div>

    <!-- Device Settings Panel -->
    <div *ngIf="showDeviceSettings()"
      class="absolute bottom-24 left-1/2 transform -translate-x-1/2 z-40 p-4 bg-black bg-opacity-80 rounded-xl w-80 shadow-2xl">
      <h3 class="text-white text-lg font-semibold mb-3">Speaker Settings</h3>
      <div class="mb-4">
        <label class="text-white text-sm block mb-1">Select a speaker</label>
        <div class="space-y-2 max-h-40 overflow-y-auto bg-gray-800 p-3 rounded-md">
          <div *ngFor="let speaker of speakers()">
            <div class="flex items-center">
              <input type="radio" [id]="'speaker-' + speaker.id" name="speaker-selection"
                [checked]="selectedSpeakerId() === speaker.id" (change)="changeSpeaker(speaker.id)"
                class="mr-2 h-5 w-5 accent-blue-500" />
              <label [for]="'speaker-' + speaker.id" class="text-white text-sm">{{ speaker.name }}</label>
            </div>
          </div>
          <div *ngIf="speakers().length === 0" class="text-gray-400 text-sm p-2">No speakers found</div>
        </div>
        <div class="flex items-center mt-4 mb-2">
          <input type="checkbox" id="speaker-toggle" [checked]="callState().isSpeakerOn" (change)="toggleSpeaker()"
            class="mr-2 h-5 w-5 accent-blue-500" />
          <label for="speaker-toggle" class="text-white text-sm font-medium">
            {{ callState().isSpeakerOn ? "Sound enabled" : "Sound disabled (muted)" }}
          </label>
        </div>
      </div>
      <div class="flex gap-2">
        <button class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
          (click)="toggleDeviceSettings()">Close</button>
      </div>
    </div>

    <!-- Footer with Call Controls -->
    <div class="absolute bottom-0 left-0 w-full z-10 flex justify-center p-4">
      <div class="flex justify-center gap-4 bg-black bg-opacity-60 rounded-full p-2">
        <!-- Camera Toggle -->
        <button (click)="toggleCamera()" [ngClass]="getButtonClass(callState().isCameraOn)"
          [title]="callState().isCameraOn ? 'Camera On' : 'Camera Off'">
          <i [class]="callState().isCameraOn ? 'fas fa-video text-xl' : 'fas fa-video-slash text-xl'"></i>
        </button>
        <!-- Mic Toggle -->
        <button (click)="toggleMic()" [ngClass]="getButtonClass(callState().isMicOn)"
          [title]="callState().isMicOn ? 'Mic On' : 'Muted'">
          <i [class]="callState().isMicOn ? 'fas fa-microphone text-xl' : 'fas fa-microphone-slash text-xl'"></i>
        </button>
        <!-- Speaker Toggle -->
        <button (click)="openSpeakerSelection()" [ngClass]="getButtonClass(callState().isSpeakerOn)"
          title="Select Speakers">
          <i class="fas fa-volume-up text-xl"></i>
        </button>
        <!-- Disconnect -->
        <button (click)="disconnectCall()"
          class="w-14 h-14 rounded-full bg-red-600 text-white flex items-center justify-center hover:scale-110 transition-transform duration-200"
          title="End Call">
          <i class="fas fa-phone-slash text-xl"></i>
        </button>
      </div>
    </div>
  </ng-container>
</div>