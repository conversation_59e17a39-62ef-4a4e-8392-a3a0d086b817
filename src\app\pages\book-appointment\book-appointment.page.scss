// Main booking page styles
.booking-content {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-attachment: fixed;
  padding-left: 24px;
  padding-right: 24px;
}

// Set the gradient background using the --background variable on ion-content
ion-content {
  --background: transparent !important;
  background: transparent !important;
  background-color: transparent !important;
}

// Header styling - Fully transparent
ion-header {
  position: relative;
  background: transparent !important;
  margin-top: 28px;

  // Remove glassmorphism background effect for full transparency
  &::before {
    display: none;
  }

  ion-toolbar {
    --background: transparent !important;
    --ion-toolbar-background: transparent !important;
    --color: #ffffff;
    --border-width: 0 0 1px 0;
    --border-style: solid;
    --border-color: #16425d;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --padding-start: 12px;
    --padding-end: 16px;
    background: transparent;
    min-height: 56px;

    ion-title {
      --color: #ffffff;
      font-size: 20px;
      font-weight: 500;
      margin-left: -150px;
      color: #ffffff;
      text-align: center;
      padding: 0 0 0 0;
      margin-bottom: -6px;
    }

    ion-buttons {
      ion-button {
        --color: #ffffff;
        --background: transparent;
        --background-hover: rgba(255, 255, 255, 0.1);
        --background-activated: rgba(255, 255, 255, 0.2);
        --border-radius: 8px;
        --padding-start: 8px;
        --padding-end: 8px;

        ion-icon {
          font-size: 24px;
        }
      }
    }
  }
}

// Appointment tabs styling
.appointment-tabs {
  padding: 0px 16px 0;

  .custom-segment {
    --background: transparent !important;
    border-radius: 0 !important;
    padding: 0 !important;
    background: transparent !important;

    .segment-button {
      --background: transparent !important;
      --background-checked: rgba(255, 255, 255, 0.1) !important;
      --color: rgba(255, 255, 255, 0.7) !important;
      --color-checked: #ffffff !important;
      --border-radius: 12px !important;
      --padding-top: 12px !important;
      --padding-bottom: 12px !important;
      font-weight: 400 !important;
      font-size: 16px !important;
      border-radius: 12px !important;
      margin: 0 !important;
      background: transparent !important;
      font-family: 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

      ion-label {
        margin: 0 !important;
        font-weight: 500 !important;
        font-size: 14px !important;
      }
    }
  }
}

// Global segment button overrides for proper styling
ion-segment-button {
  --background: transparent !important;
  --background-checked: rgba(255, 255, 255, 0.1) !important;
  --color: rgba(255, 255, 255, 0.7) !important;
  --color-checked: #ffffff !important;
  --border-radius: 12px !important;
  --padding-top: 12px !important;
  --padding-bottom: 12px !important;
  --padding-start: 16px !important;
  --padding-end: 16px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  border-radius: 12px !important;
  margin: 0 !important;
  min-height: 40px !important;
  background: transparent !important;

  &.segment-button-checked {
    --background: rgba(255, 255, 255, 0.1) !important;
    --color: #ffffff !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
  }

  ion-label {
    margin: 0 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    color: inherit !important;
  }
}

// Online Filter Section
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px;
  padding-bottom: 5px;
  padding-top: 5px;
  gap: 12px;

  .custom-dropdown {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 10px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 150px;
    max-width: 150px;
    min-width: 150px;
    height: 32px;
    box-sizing: border-box;

    &:hover {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.25);
    }

    &:active {
      background: rgba(255, 255, 255, 0.15);
    }

    &:active {
      background: rgba(255, 255, 255, 0.8);
      border-color: white;
      border-width: 1px
    }

    &.selected {
      background: rgba(255, 255, 255, 0.2);
      border-color: white;
      border-width: 1px
    }

    .dropdown-text {
      color: #ffffff;
      font-size: 12px;
      font-weight: 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }

    .dropdown-arrow {
      color: rgba(255, 255, 255, 0.8);
      margin-left: 8px;
      flex-shrink: 0;
      font-size: 16px;
    }
  }

  .custom-filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 22px;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.25);
    }

    .filter-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(0deg) brightness(100%) contrast(100%) opacity(0.9);
    }
  }
}

// In-Person Filter Section
.inperson-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px;
  gap: 8px;

  .custom-dropdown {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 10px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 28px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 150px; 
    max-width: 150px;
    min-width: 150px;
    height: 32px;
    box-sizing: border-box;

    &:hover {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.25);
    }

    &:active {
      background: rgba(255, 255, 255, 0.8);
      border-color: white;
      border-width: 1px
    }

    &.selected {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.9);
      box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
      .dropdown-text {
        color: white;
        font-weight: 500;
      }
    }

    .dropdown-text {
      color: #ffffff;
      font-size: 12px;
      font-weight: 400;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }

    .dropdown-arrow {
      color: rgba(255, 255, 255, 0.8);
      margin-left: 8px;
      flex-shrink: 0;
      font-size: 16px;
    }
  }

  .custom-filter-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 4px;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.25);
    }

    .filter-icon {
      width: 25px;
      height: 25px;
      filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(0deg) brightness(100%) contrast(100%) opacity(0.9);
    }
  }
}

// Search section (for online only)
.search-section {
  padding: 0 24px 16px 24px;
  padding-bottom: 24px;

  .custom-searchbar {
    --background: rgba(255, 255, 255, 0.08) !important;
    --color: #ffffff !important;
    --placeholder-color: rgba(255, 255, 255, 0.6) !important;
    --icon-color: rgba(255, 255, 255, 0.6) !important;
    --border-radius: 12px !important;
    --box-shadow: none !important;
    --padding-start: 16px !important;
    --padding-end: 16px !important;
    --padding-top: 0px !important;
    --padding-bottom: 0px !important;

    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border: 0px solid rgba(255, 255, 255, 0.15) !important;
    border-radius: 12px !important;
    margin: 0 !important;
    padding: 0 !important;
    background: rgba(255, 255, 255, 0.08) !important;

    &.sc-ion-searchbar-md-h {
      padding: 0 !important;
      background: rgba(255, 255, 255, 0.08) !important;
    }

    .searchbar-input {
      font-size: 14px !important;
      font-weight: 400 !important;
      padding: 0 !important;
      color: #ffffff !important;
      background: transparent !important;
    }

    .searchbar-input-container {
      padding: 0 !important;
      background: transparent !important;
    }

    .searchbar-search-icon {
      color: rgba(255, 255, 255, 0.6) !important;
    }

    .searchbar-clear-button {
      color: rgba(255, 255, 255, 0.6) !important;
    }
  }
}

// Global searchbar overrides
ion-searchbar {
  --background: rgba(255, 255, 255, 0.08) !important;
  --color: #ffffff !important;
  --placeholder-color: rgba(255, 255, 255, 0.6) !important;
  --icon-color: rgba(255, 255, 255, 0.6) !important;
  --border-radius: 12px !important;
  --box-shadow: none !important;
}



// Empty state message
.inperson-empty-message {
  position: fixed;
  top: calc(50% + 60px); /* Account for header height */
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  width: 90%;
  padding: 0 20px;
  box-sizing: border-box;
  font-weight: 300;
  z-index: 10;
}

// Date selection
.date-selection {
  margin: 0;
  padding: 0 24px 16px;

  .date-scroll {
    display: flex;
    gap: 5px;
    overflow-x: auto;
    padding: 0 0 0px 0;

    &::-webkit-scrollbar {
      display: none;
    }

    // Universal selector for all direct children (button, ion-button, etc.)
    > * {
      background: rgba(255, 255, 255, 0.05) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      color: #fff !important;
      box-shadow: none !important;
      background-clip: padding-box !important;
      border-radius: 8px !important;
      min-width: 48px;
      height: 72px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    > *.selected {
      background: rgba(255, 255, 255, 0.25) !important;
      border-color: rgba(255, 255, 255, 0.9) !important;
      border-width: 2px !important;
      color: #fff !important;
    }
    > *:hover,
    > *:focus,
    > *:active {
      background: rgba(255, 255, 255, 0.08) !important;
      background: rgba(255, 255, 255, 0.25) !important;
    }
  }
}

// Results count
.results-count {
  padding: 0 24px 16px;
  
  span {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 400;
  }
}

// Online Doctor List Section (matching in-person styling exactly)
.online-doctor-list {
  padding: 0 24px 32px 24px;
  background: transparent;

  .online-doctor-card {
    // Main container matching Figma VCP Appointment
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
    gap: 8px;
    width: 100%;
    height: 136px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 8px;
    margin-bottom: 24px;
    cursor: pointer;

    .doctor-card-content {
      // Frame 1948760707 - Main content container
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0px;
      gap: 8px;
      width: 100%;
      height: 104px;

      .doctor-main-info {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 104px;

        .doctor-left-section {
          // Frame 1948760718
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          padding: 0px;
          gap: 8px;
          width: 227px;
          height: 104px;

          .doctor-avatar {
            // Frame 1948760715 + Ellipse 18
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 8px;
            width: 40px;
            height: 40px;

            img {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
            }
          }

          .doctor-details {
            // Frame 1948760716
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 16px;
            width: 179px;
            height: 104px;

            .doctor-name-specialty {
              // Frame 1948761579
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              padding: 0px;
              gap: 8px;
              width: 179px;
              height: 64px;

              .doctor-name {
                // Dr. Abdur Raheem
                min-width: 132px;
                height: 24px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 500;
                font-size: 16px;
                line-height: 24px;
                display: flex;
                align-items: center;
                color: #FCFCFC;
                margin: 0;
                flex: 1;
              }

              .doctor-specialty {
                // General Physician
                width: 96px;
                height: 16px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                color: #9E9FA9;
                margin: 0;
                margin-top: -12px;
              }
            }

            .doctor-languages {
              // Frame 1948760741
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              padding: 0px;
              gap: 4px;
              width: 102px;
              height: 16px;

              ion-icon {
                width: 16px;
                height: 16px;
                color: #FCFCFC;
              }

              span {
                // English, Arabic
                min-width: 82px;
                height: 16px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 500;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                color: #FCFCFC;
                flex: 1;
              }
            }

            .doctor-availability {
              // Frame 1000008826
              display: flex;
              flex-direction: row;
              align-items: center;
              padding: 0px;
              gap: 4px;
              width: 240px;
              height: 24px;

              .available-text {
                // Earliest available at:
                width: 109px;
                height: 16px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                color: #9E9FA9;
                margin-bottom: 15px;
              }

              .time-chip {
                // Selector
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 6px 2px;
                gap: 8px;
                width: 66px;
                height: 24px;
                background: rgba(255, 255, 255, 0.25);
                border: 1px solid #FFFFFF;
                border-radius: 8px;
                margin-bottom: 15px;

                // unit text
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 500;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                text-align: center;
                color: #FCFCFC;
              }
            }
          }
        }

        .doctor-right-section {
          // Frame 1948761173
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-end;
          padding: 0px;
          width: 57px;
          height: 104px;

          .doctor-rating {
            // Frame 1948760705
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 0px;
            gap: 2px;
            width: 57px;
            height: 24px;

            ion-icon {
              width: 12px;
              height: 12px;
              font-size: 12px;
              color: #FFA800;
              display: flex;
              align-items: center;
              line-height: 16px;
              vertical-align: top;
              margin-top: -3.2px;
              border-radius: 2px;
              filter: drop-shadow(0 0 1px rgba(255, 168, 0, 0.3));
            }

            .rating-text {
              // 4.9 (40)
              width: 39px;
              height: 16px;
              font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              font-style: normal;
              font-weight: 400;
              font-size: 12px;
              line-height: 16px;
              display: flex;
              align-items: center;
              color: #9E9FA9;
            }
          }

          .arrow-icon {
            width: 24px;
            height: 24px;
            color: #FCFCFC;
          }
        }
      }
    }
  }
}

// Minimal global overrides (keep only essential ones)
ion-button {
  --text-transform: none;
  text-transform: none;
}

ion-segment {
  --background: transparent;
  background: transparent;
}

ion-searchbar.custom-searchbar {
  --background: rgba(255, 255, 255, 0.08);
  --color: #ffffff;
  --placeholder-color: rgba(255, 255, 255, 0.6);
  --icon-color: rgba(255, 255, 255, 0.6);
  --border-radius: 12px;
  --box-shadow: none;
}
