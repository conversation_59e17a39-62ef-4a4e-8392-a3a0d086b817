import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonContent,
  IonButton,
  IonIcon,
  ModalController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { videocam, mic, headset, volumeOff } from 'ionicons/icons';

@Component({
  selector: 'app-consultation-tips-modal',
  templateUrl: './consultation-tips-modal.component.html',
  styleUrls: ['./consultation-tips-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonButton,
    IonIcon
  ]
})
export class ConsultationTipsModalComponent {

  constructor(private modalController: ModalController) {
    addIcons({
      videocam,
      mic,
      headset,
      volumeOff
    });
  }

  confirm() {
    this.modalController.dismiss();
  }
}
