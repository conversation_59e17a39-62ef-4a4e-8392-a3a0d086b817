import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  IonContent,
  IonButton,
  IonIcon,
  ModalController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { videocam, mic, headset, volumeOff } from 'ionicons/icons';

@Component({
  selector: 'app-consultation-tips-modal',
  templateUrl: './consultation-tips-modal.component.html',
  styleUrls: ['./consultation-tips-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonButton,
    IonIcon
  ]
})
export class ConsultationTipsModalComponent {

  constructor(
    private modalController: ModalController,
    private router: Router
  ) {
    addIcons({
      videocam,
      mic,
      headset,
      volumeOff
    });
  }

  confirm() {
    // Dismiss the modal first
    this.modalController.dismiss();

    // Navigate to waiting page
    this.router.navigate(['/waiting']);
  }
}
