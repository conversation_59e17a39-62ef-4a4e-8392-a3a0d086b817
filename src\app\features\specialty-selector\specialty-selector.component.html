<ion-content class="specialty-selector-content">
  <div class="specialty-selector-container">
    <!-- Header -->
    <div class="selector-header">
      <h3>Select Speciality</h3>
    </div>
    
    <!-- Specialty Options -->
    <div class="specialty-options">
      <div 
        *ngFor="let specialty of specialties" 
        class="specialty-option"
        (click)="selectSpecialty(specialty)">
        <span>{{ specialty }}</span>
      </div>
    </div>
    
    <!-- Close Button -->
    <div class="close-button-container">
      <ion-button 
        expand="block" 
        class="close-button"
        (click)="close()">
        Close
      </ion-button>
    </div>
  </div>
</ion-content>
