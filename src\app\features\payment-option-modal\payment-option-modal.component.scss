// Payment Option Modal Styles
.payment-option-content {
  --background: #031934;
  --color: #ffffff;
  background: #031934;
  border-radius: 24px 24px 0 0;
  
  .payment-option-container {
    padding: 24px 24px 32px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 280px;
    background: #031934;
    border-radius: 24px 24px 0 0;
    
    // Caution Icon
    .caution-icon-container {
      margin-bottom: 24px;

      .caution-icon {
        width: 48px;
        height: 48px;
        display: block;
      }
    }
    
    // Title
    .payment-title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 500;
      line-height: 1.4;
      margin-bottom: 32px;
      max-width: 300px;
    }
    
    // Buttons Container
    .payment-buttons {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      // Use Insurance Button (same as book appointment button)
      .insurance-button {
        --background: #004AD9;
        --background-activated: #0056CC;
        --background-hover: #0056CC;
        --color: #ffffff;
        --border-radius: 8px;
        --padding-top: 16px;
        --padding-bottom: 16px;
        --text-transform: none;

        font-size: 18px;
        font-weight: 500;
        height: 52px;
        margin: 0;
        text-transform: none;
      }
      
      // Self Pay Button (same as filters apply button)
      .self-pay-button {
        --background: rgba(255, 255, 255, 0.25);
        --background-activated: rgba(255, 255, 255, 0.25);
        --background-hover: rgba(255, 255, 255, 0.2);
        --color: #ffffff;
        --border-radius: 8px;
        --padding-top: 16px;
        --padding-bottom: 16px;
        --text-transform: none;

        font-size: 18px;
        font-weight: 500;
        height: 52px;
        margin: 0;
        text-transform: none;
      }
    }
  }
}
