<ion-content class="ion-no-padding" fullscreen scroll-y="false">
  <!-- Book Appointment Header -->
  <div class="app-header">
    <h1 class="app-title">Book Appointment</h1>
  </div>

  @if (this.client == 1) {
    <ng-container>
      <div class="splash-container">
        <div class="logo">
          <img src="../../../assets/images/splash-logo.svg" alt="Logo" />
          <p>{{'splash.please-wait' | translate}}</p>
        </div>
      </div>
    </ng-container>
  }
  @if (this.client == 2 ) {
    <ng-container>
      <div [ngClass]="darkMode ? 'pura-splash-dark' : 'pura-splash-light'"  class="splash-container-pura">
       
    <div class="logo">
      <!-- <img src="../../../assets/images/pura-logo.svg" alt="Logo" /> -->
       @if(darkMode){
        <i   class="das das-carbon_task-complete pura-icon-dark"></i>
       }@else {
        <i   class="das das-carbon_task-complete pura-icon-light"></i>
       }
       @if(darkMode){
        <p class="pura-text-dark" >{{'splash.please-wait' | translate}}</p>
       }@else {
        <p class="pura-text-light" >{{'splash.please-wait' | translate}}</p>
       }
      <!-- <i [ngClass]="darkMode ? 'pura-icon-dark' : 'pura-icon-light'"  class="das das-carbon_task-complete"></i> -->
      <!-- <p [ngClass]="darkMode ? 'pura-text-dark' : 'pura-text-light'" >{{'splash.please-wait' | translate}}</p> -->
    </div>

  </div>
 
    </ng-container>
  }
  <!-- @if (this.client == 2 && darkMode) {
    <ng-container>
      <div class="splash-container-pura">
      
        <div class="logo">
          <img src="../../../assets/images/pura-logo.svg" alt="Logo" />
          <p>Starting up, please wait!</p>
        </div>
      </div>
    </ng-container>
  } -->
</ion-content>
