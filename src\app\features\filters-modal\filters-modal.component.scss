// Filters Modal Styles
.filters-header {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  margin-top: 28px;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
  position: relative;

  // Black overlay from top of modal to bottom of toolbar
  &::before {
    content: '';
    position: absolute;
    top: -28px; // Extend up to cover the margin-top area
    left: 0;
    right: 0;
    bottom: 0; // This will go to the bottom of the header including toolbar
    background: rgba(0, 0, 0, 0.04);
    z-index: 0;
  }

  .filters-toolbar {
    --background: transparent;
    --color: #ffffff;
    --border-width: 0;
    --border-style: solid;
    --border-color: #16425d;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --padding-start: 12px;
    --padding-end: 16px;
    background: transparent;
    min-height: 56px;
    position: relative;
    z-index: 1;

    .filters-title {
      --color: #ffffff;
      font-size: 21px;
      font-weight: 500;
      padding: 0 0 0 0;
      margin-bottom: -6px;
    }

    ion-button {
      --color: #ffffff;
      --background: transparent;

      ion-icon {
        font-size: 24px;
      }
    }
  }
}

.filters-content {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  
  .filters-container {
    padding: 24px 16px;
    
    .filter-section {
      margin-bottom: 32px;
      
      .filter-label {
        color: #ffffff;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
      }
      
      .filter-dropdown {
        position: relative;
        background: transparent;
        border: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.6);
        border-radius: 0px;
        padding: 12px 0;
        min-height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        .dropdown-text {
          color: #ffffff;
          font-size: 14px;
          font-weight: 400;
          flex: 1;
        }

        .dropdown-icon {
          color: rgba(255, 255, 255, 0.7);
          font-size: 20px;
          margin-left: 8px;
        }
      }
    }
  }
  
  .apply-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: transparent;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    
    .apply-button {
      --background: rgba(255, 255, 255, 0.25);
      --background-activated: rgba(255, 255, 255, 0.25);
      --background-hover: rgba(255, 255, 255, 0.2);
      --color: #ffffff;
      --border-radius: 12px;
      --padding-top: 16px;
      --padding-bottom: 16px;
      --text-transform: none;

      font-size: 16px;
      font-weight: 600;
      height: 52px;
      margin: 0;
      text-transform: none;
    }
  }
}

// Override Ionic select popover styles
ion-popover.select-popover {
  --background: #2d4a66;
  --color: #ffffff;
  
  .popover-content {
    background: #2d4a66;
    
    ion-radio-group {
      background: #2d4a66;
      
      ion-item {
        --background: #2d4a66;
        --color: #ffffff;
        --border-color: rgba(255, 255, 255, 0.1);
        
        ion-label {
          color: #ffffff;
        }
        
        ion-radio {
          --color: #ffffff;
          --color-checked: #4a90e2;
        }
      }
    }
  }
}
