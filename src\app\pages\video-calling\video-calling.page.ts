import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild, ElementRef, signal, effect, WritableSignal, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonSpinner,
  IonRadioGroup,
  IonItem,
  IonRadio,
  IonLabel,
  IonCheckbox
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import {
  chevronBack,
  videocam,
  videocamOff,
  mic,
  micOff,
  volumeHigh,
  call,
  cameraReverse
} from 'ionicons/icons';
import {
    CallClient,
    CallAgent,
    LocalVideoStream,
    VideoStreamRenderer,
    Call,
    RemoteVideoStream,
    DeviceManager,
    RemoteParticipant as AcsRemoteParticipant,
} from '@azure/communication-calling';
import { AzureCommunicationTokenCredential } from '@azure/communication-common';

// --- TYPE DEFINITIONS ---
interface FormData {
    userId: string;
    accessToken: string;
    groupId: string;
}

interface CallState {
    isConnected: boolean;
    isConnecting: boolean;
    isCameraOn: boolean;
    isMicOn: boolean;
    isSpeakerOn: boolean;
}

interface DeviceInfo {
    id: string;
    name: string;
}

interface LocalPosition {
    top: number;
    right: number;
}

@Component({
    selector: 'app-video-calling',
    standalone: true,
    imports: [
        CommonModule,
        IonHeader,
        IonToolbar,
        IonTitle,
        IonContent,
        IonButtons,
        IonButton,
        IonIcon,
        IonSpinner,
        IonRadioGroup,
        IonItem,
        IonRadio,
        IonLabel,
        IonCheckbox
    ],
    templateUrl: './video-calling.page.html',
    styleUrls: ['./video-calling.page.scss']
})
export class VideoCallingComponent implements OnInit, OnDestroy {
    // --- VIEW REFERENCES ---
    @ViewChild('remoteVideoContainer') remoteVideoRef!: ElementRef<HTMLDivElement>;
    @ViewChild('localVideoContainer') localVideoRef!: ElementRef<HTMLDivElement>;

    // --- STATE MANAGEMENT (using Angular Signals) ---
    formData: WritableSignal<FormData> = signal({ userId: '', accessToken: '', groupId: '' });
    callState: WritableSignal<CallState> = signal({
        isConnected: false,
        isConnecting: false,
        isCameraOn: true,
        isMicOn: true,
        isSpeakerOn: true
    });
    localPosition: WritableSignal<LocalPosition> = signal({ top: 72, right: 16 });
    microphones = signal<DeviceInfo[]>([]);
    speakers = signal<DeviceInfo[]>([]);
    cameras = signal<DeviceInfo[]>([]);
    selectedMicrophoneId = signal<string>('');
    selectedSpeakerId = signal<string>('');
    selectedCameraId = signal<string>('');
    showDeviceSettings = signal<boolean>(false);
    hasAttemptedConnection = signal<boolean>(false);

    // --- AZURE COMMUNICATION SERVICES (ACS) INSTANCES ---
    private callClient: CallClient | null = null;
    private callAgent: CallAgent | null = null;
    private currentCall: Call | null = null;
    private deviceManager: DeviceManager | null = null;
    private localVideoStream: LocalVideoStream | null = null;
    private localRenderer: VideoStreamRenderer | null = null;
    private remoteRenderer: VideoStreamRenderer | null = null;
    private audioObserver: MutationObserver | null = null;

    constructor(private cdr: ChangeDetectorRef, private router: Router) {
        addIcons({
            chevronBack,
            videocam,
            videocamOff,
            mic,
            micOff,
            volumeHigh,
            call,
            cameraReverse
        });
        // Effect to handle muting/unmuting of dynamically added audio elements
        effect(() => {
            const isConnected = this.callState().isConnected;
            const isSpeakerOn = this.callState().isSpeakerOn;

            if (isConnected) {
                this.applyMuteStateToAudioElements(isSpeakerOn);
                this.setupAudioObserver(isSpeakerOn);
            } else {
                this.audioObserver?.disconnect();
                this.audioObserver = null;
            }
        });
    }

    ngOnDestroy() {
        // Cleanup when the component is destroyed.
        this.disconnectCall();
        this.audioObserver?.disconnect();
    }

    /**
     * Fetches ACS connection details from an API and initiates the call.
     * @param role - The role of the user (e.g., 'PATIENT', 'DOCTOR').
     */
    async getCallInfo(role: 'PATIENT' | 'DOCTOR') {
        this.callState.update(s => ({ ...s, isConnecting: true }));
        this.hasAttemptedConnection.set(true);
        try {
            // const response = await fetch(`**********************************/vcp/api/open-api/acs-details?role=${role}`, {
            //     headers: {
            //         moduletype: 'pura',
            //         'x-api-key': 'f5801458-7831-4d97-a9f1-553892e326e8',
            //     },
            // });

            // if (!response.ok) {
            //     throw new Error(`HTTP error! status: ${response.status}`);
            // }

            // const { data } = await response.json();
            const newFormData = {
                userId: '8:acs:9e3c5820-3b5c-41e8-9354-9895c78c4dab_00000028-b236-0d31-952b-63bd45601867', // data.acsUserId,
                accessToken: '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', //data.token,
                groupId:'f5801458-7831-4d97-a9f1-553892e326e9' //data.acsRoomId,
            };
            this.formData.set(newFormData);
            await this.connectCall();
        } catch (error) {
            console.error('Fetch error:', error);
            this.callState.update(s => ({ ...s, isConnecting: false }));
        }
    }

    /**
     * Initializes the ACS CallClient and CallAgent.
     */
    private async initializeCallClient(): Promise<boolean> {
        try {
            // Clean up any existing instances first
            if (this.callAgent) {
                try {
                    this.callAgent.dispose();
                } catch (e) {
                    console.warn("Error disposing existing call agent:", e);
                }
                this.callAgent = null;
            }

            if (this.callClient) {
                try {
                    this.callClient.dispose();
                } catch (e) {
                    console.warn("Error disposing existing call client:", e);
                }
                this.callClient = null;
            }

            // Create new instances
            const tokenCredential = new AzureCommunicationTokenCredential(this.formData().accessToken);
            this.callClient = new CallClient();
            this.callAgent = await this.callClient.createCallAgent(tokenCredential, { displayName: this.formData().userId });
            this.deviceManager = await this.callClient.getDeviceManager();

            await this.getAudioDevices();
            await this.getCameraDevices();

            console.log("Call client initialized successfully");
            return true;
        } catch (err) {
            console.error("Failed to initialize call client:", err);
            return false;
        }
    }

    /**
     * Connects to the ACS group call.
     */
    async connectCall() {
        const currentFormData = this.formData();
        if (!currentFormData.userId || !currentFormData.accessToken || !currentFormData.groupId) {
            console.warn("Missing Fields: Please provide all required information.");
            this.callState.update(s => ({ ...s, isConnecting: false }));
            return;
        }

        const ok = await this.initializeCallClient();
        if (!ok) {
            this.callState.update(s => ({ ...s, isConnecting: false }));
            return;
        }

        if (this.callState().isCameraOn) {
            await this.prepareLocalVideoStream();
        }

        try {
            if (!this.callAgent) {
                console.error("Call agent not initialized");
                this.callState.update(s => ({ ...s, isConnecting: false }));
                return;
            }

            const call = await this.callAgent.join(
                { groupId: currentFormData.groupId },
                {
                    videoOptions: { localVideoStreams: this.localVideoStream ? [this.localVideoStream] : [] },
                    audioOptions: { muted: !this.callState().isMicOn }
                }
            );

            if (!call) {
                 this.callState.update(s => ({ ...s, isConnecting: false }));
                 return;
            }
            this.currentCall = call;

            this.subscribeToRemoteParticipants(call);

            // [FIX] Set connected state and trigger change detection to render the UI
            this.callState.update(s => ({ ...s, isConnected: true, isConnecting: false }));
            this.cdr.detectChanges();

            // [FIX] Now that the UI is rendered, we can safely render the local video
            if (this.callState().isCameraOn && this.localVideoStream) {
                await this.renderLocalVideoStream();
            }

            console.log("Connected: Call started.");

        } catch (err) {
            console.error("Failed to join call:", err);
            this.callState.update(s => ({ ...s, isConnecting: false }));
        }
    }

    /**
     * Prepares the local video stream before joining the call.
     */
    private async prepareLocalVideoStream() {
        if (!this.deviceManager) return;
        try {
            const devices = await this.deviceManager.getCameras();
            if (devices.length === 0) {
                console.warn("No cameras found.");
                return;
            }
            let camera = devices.find(cam => cam.id === this.selectedCameraId());
            if (!camera) {
                camera = devices[0];
                this.selectedCameraId.set(camera.id);
            }
            this.localVideoStream = new LocalVideoStream(camera);
        } catch (err) {
            console.error("Error initializing local video stream:", err);
        }
    }

    /**
     * Renders the local video stream to the designated container.
     */
    private async renderLocalVideoStream() {
        if (!this.localVideoStream) {
            console.warn("Attempted to render but no local video stream available.");
            return;
        }
        if (!this.localVideoRef) {
            console.error("Attempted to render but local video container is not available.");
            return;
        }
        try {
            this.localRenderer = new VideoStreamRenderer(this.localVideoStream);
            // [FIX] Use scalingMode 'Crop' to ensure the video fills its container.
            const view = await this.localRenderer.createView({ scalingMode: 'Crop' });

            // Ensure the container is clean before appending.
            this.localVideoRef.nativeElement.innerHTML = '';
            this.localVideoRef.nativeElement.appendChild(view.target);

        } catch (err) {
            console.error("Error rendering local video:", err);
        }
    }

    /**
     * Subscribes to events for remote participants in the call.
     */
    private subscribeToRemoteParticipants(call: Call) {
        const handleStream = (stream: RemoteVideoStream) => {
            if (stream.isAvailable) {
                this.renderRemoteVideoStream(stream);
            }
            stream.on('isAvailableChanged', () => {
                if (stream.isAvailable) {
                    this.renderRemoteVideoStream(stream);
                } else {
                    this.clearRemoteVideo();
                }
            });
        };

        const subscribeToParticipant = (participant: AcsRemoteParticipant) => {
            participant.videoStreams.forEach(handleStream);
            participant.on('videoStreamsUpdated', e => {
                e.added.forEach(handleStream);
                e.removed.forEach(() => this.clearRemoteVideo());
            });
        };

        call.remoteParticipants.forEach(subscribeToParticipant);
        call.on('remoteParticipantsUpdated', e => {
            e.added.forEach(subscribeToParticipant);
            e.removed.forEach(() => this.clearRemoteVideo());
        });
    }

    ngOnInit() {
        // Component initialization
    }

    ionViewWillEnter() {
        // Reset state when entering the page
        this.hasAttemptedConnection.set(false);

        // Automatically join as patient when entering the page
        setTimeout(() => {
            this.getCallInfo('PATIENT');
        }, 1000); // Small delay to ensure UI is ready
    }

    goBack() {
        // Navigate back to main page
        this.router.navigate(['/online-doctor']);
    }

    // --- RECONNECTION METHODS ---

    /**
     * Reconnects to the call after disconnection
     */
    async reconnectCall() {
        if (this.callState().isConnecting || this.callState().isConnected) {
            console.log("Already connecting or connected");
            return;
        }

        console.log("Attempting to reconnect...");
        await this.getCallInfo('PATIENT');
    }

    /**
     * Renders a remote participant's video stream.
     */
    private async renderRemoteVideoStream(stream: RemoteVideoStream) {
        this.clearRemoteVideo();
        this.remoteRenderer = new VideoStreamRenderer(stream);
        try {
            const view = await this.remoteRenderer.createView({ scalingMode: 'Crop' });
            if (this.remoteVideoRef) {
                this.remoteVideoRef.nativeElement.innerHTML = '';
                this.remoteVideoRef.nativeElement.appendChild(view.target);
            }
        } catch (err) {
            console.error("Error rendering remote stream", err);
            this.remoteRenderer?.dispose();
        }
    }

    /**
     * Clears the remote video display.
     */
    private clearRemoteVideo() {
        this.remoteRenderer?.dispose();
        this.remoteRenderer = null;
        if (this.remoteVideoRef) {
            this.remoteVideoRef.nativeElement.innerHTML = '';
        }
    }

    /**
     * Disconnects from the current call and resets the state.
     */
    async disconnectCall() {
        try {
            await this.currentCall?.hangUp();
        } catch (e) {
            console.error("Error hanging up:", e);
        }

        // Clean up video streams and renderers
        this.localRenderer?.dispose();
        this.localRenderer = null;
        this.localVideoStream?.dispose();
        this.localVideoStream = null;
        this.clearRemoteVideo();
        this.currentCall = null;

        // Properly dispose of call agent and client
        try {
            this.callAgent?.dispose();
            this.callAgent = null;
        } catch (e) {
            console.error("Error disposing call agent:", e);
        }

        try {
            this.callClient?.dispose();
            this.callClient = null;
        } catch (e) {
            console.error("Error disposing call client:", e);
        }

        this.deviceManager = null;

        this.callState.set({
            isConnected: false,
            isConnecting: false,
            isCameraOn: true,
            isMicOn: true,
            isSpeakerOn: true
        });
        this.selectedMicrophoneId.set('');
        this.selectedSpeakerId.set('');
        this.selectedCameraId.set('');

        console.log("Disconnected: Call ended and resources cleaned up.");

        // Redirect to homepage after call ends
        setTimeout(() => {
            this.router.navigate(['/online-doctor']);
        }, 500); // Small delay to ensure cleanup is complete
    }

    // --- DEVICE CONTROL METHODS ---

    async getAudioDevices() {
        if (!this.deviceManager) return;
        try {
            const mics = await this.deviceManager.getMicrophones();
            this.microphones.set(mics.map(mic => ({ id: mic.id, name: mic.name || `Microphone (${mic.id.slice(0, 8)})` })));
            if (mics.length > 0 && !this.selectedMicrophoneId()) this.selectedMicrophoneId.set(mics[0].id);

            const speakers = await this.deviceManager.getSpeakers();
            this.speakers.set(speakers.map(spk => ({ id: spk.id, name: spk.name || `Speaker (${spk.id.slice(0, 8)})` })));
            if (speakers.length > 0 && !this.selectedSpeakerId()) this.selectedSpeakerId.set(speakers[0].id);
        } catch (err) {
            console.error("Error getting audio devices:", err);
        }
    }

    async getCameraDevices() {
        if (!this.deviceManager) return;
        try {
            const cameras = await this.deviceManager.getCameras();
            this.cameras.set(cameras.map(cam => ({ id: cam.id, name: cam.name || `Camera (${cam.id.slice(0, 8)})` })));
            if (cameras.length > 0 && !this.selectedCameraId()) this.selectedCameraId.set(cameras[0].id);
        } catch (err) {
            console.error("Error getting camera devices:", err);
        }
    }

    async changeMicrophone(deviceId: string) {
        if (!this.deviceManager) return;
        try {
            const mic = (await this.deviceManager.getMicrophones()).find(m => m.id === deviceId);
            if (mic) {
                await this.deviceManager.selectMicrophone(mic);
                this.selectedMicrophoneId.set(deviceId);
            }
        } catch (err) {
            console.error("Error changing microphone:", err);
        }
    }

    async changeSpeaker(deviceId: string) {
        if (!this.deviceManager) return;
        try {
            const speaker = (await this.deviceManager.getSpeakers()).find(s => s.id === deviceId);
            if (speaker) {
                await this.deviceManager.selectSpeaker(speaker);
                this.selectedSpeakerId.set(deviceId);
                this.callState.update(s => ({ ...s, isSpeakerOn: true }));
            }
        } catch (err) {
            console.error("Error changing speaker:", err);
        }
    }

    async changeCamera(deviceId: string) {
        if (!this.deviceManager || !this.currentCall) return;
        try {
            const camera = (await this.deviceManager.getCameras()).find(c => c.id === deviceId);
            if (!camera) {
                console.error("Selected camera could not be found.");
                return;
            }

            if (this.localVideoStream) {
                await this.currentCall.stopVideo(this.localVideoStream);
            }

            this.localRenderer?.dispose();
            this.localRenderer = null;

            const newLocalStream = new LocalVideoStream(camera);
            this.localVideoStream = newLocalStream;
            this.selectedCameraId.set(camera.id);

            if (this.callState().isCameraOn) {
                await this.currentCall.startVideo(this.localVideoStream);
                await this.renderLocalVideoStream();
            }

            console.log(`Camera changed to ${camera.name || 'selected device'}`);
        } catch (err) {
            console.error("Error changing camera:", err);
        }
    }

    async switchCamera() {
        if (!this.deviceManager || !this.callState().isConnected) return;
        try {
            const cameraDevices = this.cameras();
            if (cameraDevices.length <= 1) {
                console.log("Only one camera available, cannot switch.");
                return;
            };
            const currentIndex = cameraDevices.findIndex(c => c.id === this.selectedCameraId());
            const nextIndex = (currentIndex + 1) % cameraDevices.length;
            await this.changeCamera(cameraDevices[nextIndex].id);
        } catch (err) {
            console.error("Error switching camera:", err);
        }
    }

    // --- UI TOGGLE METHODS ---

    async toggleCamera() {
        if (!this.currentCall) return;
        const isCameraCurrentlyOn = this.callState().isCameraOn;
        try {
            if (isCameraCurrentlyOn) {
                if (this.localVideoStream) {
                    await this.currentCall.stopVideo(this.localVideoStream);
                }
                this.localRenderer?.dispose();
                this.localRenderer = null;
                if (this.localVideoRef) this.localVideoRef.nativeElement.innerHTML = '';
            } else {
                if (!this.localVideoStream) {
                    await this.prepareLocalVideoStream();
                }
                if (this.localVideoStream) {
                    await this.currentCall.startVideo(this.localVideoStream);
                    this.renderLocalVideoStream();
                }
            }
            this.callState.update(s => ({ ...s, isCameraOn: !isCameraCurrentlyOn }));
        } catch (err) {
            console.error("Error toggling camera:", err);
        }
    }

    async toggleMic() {
        if (!this.currentCall) return;
        try {
            if (this.callState().isMicOn) {
                await this.currentCall.mute();
            } else {
                await this.currentCall.unmute();
            }
            this.callState.update(s => ({ ...s, isMicOn: !s.isMicOn }));
        } catch (err) {
            console.error("Error toggling mic:", err);
        }
    }

    toggleSpeaker() {
        this.callState.update(s => ({ ...s, isSpeakerOn: !s.isSpeakerOn }));
    }

    toggleDeviceSettings() {
        this.showDeviceSettings.update(v => !v);
        if (this.showDeviceSettings() && this.callState().isConnected) {
            this.getAudioDevices();
            this.getCameraDevices();
        }
    }

    openSpeakerSelection() {
        this.showDeviceSettings.set(true);
        if (this.callState().isConnected) {
            this.getAudioDevices();
        }
    }

    // --- AUDIO OBSERVER LOGIC ---

    private applyMuteStateToAudioElements(isSpeakerOn: boolean) {
        document.querySelectorAll('audio').forEach(audio => {
            audio.muted = !isSpeakerOn;
        });
    }

    private setupAudioObserver(isSpeakerOn: boolean) {
        this.audioObserver?.disconnect();
        this.audioObserver = new MutationObserver(mutations => {
            let shouldCheck = false;
            for (const mutation of mutations) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (const node of Array.from(mutation.addedNodes)) {
                        if (node.nodeType === 1 && ((node as Element).tagName === 'AUDIO' || (node as Element).querySelector('audio'))) {
                            shouldCheck = true;
                            break;
                        }
                    }
                }
                if (shouldCheck) break;
            }
            if (shouldCheck) {
                this.applyMuteStateToAudioElements(isSpeakerOn);
            }
        });
        this.audioObserver.observe(document.body, { childList: true, subtree: true });
    }

    // --- DRAG-AND-DROP LOGIC ---

    handleDragStart(event: any) {
        event.preventDefault();
        const isTouchEvent = window.TouchEvent && event instanceof TouchEvent;
        const startX = isTouchEvent ? event.touches[0].clientX : event.clientX;
        const startY = isTouchEvent ? event.touches[0].clientY : event.clientY;
        const startPos = this.localPosition();

        const onMove = (moveEvent: MouseEvent | TouchEvent) => {
            const moveX = isTouchEvent ? (moveEvent as TouchEvent).touches[0].clientX : (moveEvent as MouseEvent).clientX;
            const moveY = isTouchEvent ? (moveEvent as TouchEvent).touches[0].clientY : (moveEvent as MouseEvent).clientY;

            const newTop = Math.min(Math.max(88, startPos.top + moveY - startY), window.innerHeight - 200);
            const newRight = Math.min(Math.max(16, startPos.right - (moveX - startX)), window.innerWidth - 136);

            this.localPosition.set({ top: newTop, right: newRight });
        };

        const onEnd = () => {
            window.removeEventListener('mousemove', onMove);
            window.removeEventListener('mouseup', onEnd);
            window.removeEventListener('touchmove', onMove);
            window.removeEventListener('touchend', onEnd);
        };

        window.addEventListener(isTouchEvent ? 'touchmove' : 'mousemove', onMove, { passive: false });
        window.addEventListener(isTouchEvent ? 'touchend' : 'mouseup', onEnd);
    }

    // --- UTILITY METHODS ---

    getButtonClass(isActive: boolean): string {
        const baseStyle = "w-14 h-14 rounded-full flex items-center justify-center transition-transform duration-200 border border-white";
        const activeStyle = "bg-[#0b2d57] text-white border-[1px] border-[#FFFFFF1A]";
        const inactiveStyle = "bg-[#4a5568] text-white border-[1px] border-white";
        return `${baseStyle} ${isActive ? activeStyle : inactiveStyle}`;
    }
}
