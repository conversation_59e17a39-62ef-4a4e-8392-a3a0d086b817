import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { BridgeService } from './bridge.service';

@Injectable({
  providedIn: 'root',
})
export class ShimmerEffectService {
  private skeletonVisibleSubject = new BehaviorSubject<boolean>(false);
  skeletonVisible$ = this.skeletonVisibleSubject.asObservable();

  private itemCountSubject = new BehaviorSubject<number>(9); // Default to 5
  itemCount$ = this.itemCountSubject.asObservable();

  private templateSubject = new BehaviorSubject<string>('dashboard');
  template$ = this.templateSubject.asObservable();
  constructor(private bridge:BridgeService){}

  show(itemCount: number, template?: any) {
    this.itemCountSubject.next(itemCount);
    this.skeletonVisibleSubject.next(true);
    this.templateSubject.next(template);
    this.bridge.themeSettingsWebSDK(true)

  }

  hide() {
    this.skeletonVisibleSubject.next(false);
    this.templateSubject.next('');
    this.bridge.themeSettingsWebSDK(false)

  }
}
