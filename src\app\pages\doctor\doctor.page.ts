import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonItem,
  IonLabel,
  IonSegment,
  IonSegmentButton,
  ModalController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { chevronBack, star, language, calendar, location } from 'ionicons/icons';
import { PaymentOptionModalComponent } from '../../features/payment-option-modal/payment-option-modal.component';

export interface Doctor {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  ratingCount: number;
  languages: string;
  earliestTime: string;
  avatar: string;
  availability?: {
    [date: string]: {
      afternoon: string[];
      evening: string[];
    };
  };
}

@Component({
  selector: 'app-doctor',
  templateUrl: './doctor.page.html',
  styleUrls: ['./doctor.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonItem,
    IonLabel,
    IonSegment,
    IonSegmentButton
  ]
})
export class DoctorPage implements OnInit {
  doctor!: Doctor;
  initialSelectedDate: string = '30/1';
  appointmentType: string = 'online';
  selectedTab: string = 'online';
  selectedSegment: string = 'in-person'; // Legacy property
  selectedDate: string = '30/1';
  selectedTime: string = '12:30 PM';
  savedPaymentMethod: string = ''; // Store payment method from previous selection

  availableDates = [
    { day: 'Today', date: '30/1', value: '30/1' },
    { day: 'Thu', date: '31/1', value: '31/1' },
    { day: 'Fri', date: '1/2', value: '1/2' },
    { day: 'Sat', date: '2/2', value: '2/2' },
    { day: 'Sun', date: '3/2', value: '3/2' },
    { day: 'Mon', date: '4/2', value: '4/2' },
    { day: 'Tue', date: '5/2', value: '5/2' },
    { day: 'Wed', date: '6/2', value: '6/2' },
    { day: 'Thu', date: '7/2', value: '7/2' },
    { day: 'Fri', date: '8/2', value: '8/2' },
    { day: 'Sat', date: '9/2', value: '9/2' },
    { day: 'Sun', date: '10/2', value: '10/2' },
    { day: 'Mon', date: '11/2', value: '11/2' },
    { day: 'Tue', date: '12/2', value: '12/2' },
    { day: 'Wed', date: '13/2', value: '13/2' },
    { day: 'Thu', date: '14/2', value: '14/2' },
    { day: 'Fri', date: '15/2', value: '15/2' },
    { day: 'Sat', date: '16/2', value: '16/2' },
    { day: 'Sun', date: '17/2', value: '17/2' },
    { day: 'Mon', date: '16/2', value: '16/2' },
    { day: 'Tue', date: '17/2', value: '17/2' },
    { day: 'Wed', date: '18/2', value: '18/2' }
  ];

  afternoonSlots: { time: string; disabled: boolean }[] = [];
  eveningSlots: { time: string; disabled: boolean }[] = [];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private modalController: ModalController
  ) {
    addIcons({
      chevronBack,
      star,
      language,
      calendar,
      location
    });
  }

  ngOnInit() {
    // Get doctor data from navigation state
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state) {
      this.doctor = navigation.extras.state['doctor'];
      this.initialSelectedDate = navigation.extras.state['selectedDate'] || '30/1';
      this.appointmentType = navigation.extras.state['appointmentType'] || 'online';

      // Set the selectedTab to match the appointmentType from book appointment page
      this.selectedTab = this.appointmentType;

      // Restore selected time if coming back from confirmation page
      if (navigation.extras.state['selectedTime']) {
        this.selectedTime = navigation.extras.state['selectedTime'];
      }

      // Store payment method if coming back from confirmation page
      if (navigation.extras.state['paymentMethod']) {
        this.savedPaymentMethod = navigation.extras.state['paymentMethod'];
      }
    }

    // If no doctor data in state, try to get from route params
    if (!this.doctor) {
      const doctorId = this.route.snapshot.paramMap.get('id');
      // In a real app, you would fetch doctor data by ID from a service
      // For now, we'll use mock data
      this.doctor = {
        id: parseInt(doctorId || '1'),
        name: 'Dr. Abdur Raheem',
        specialty: 'General Physician',
        rating: 4.9,
        ratingCount: 40,
        languages: 'English, Arabic',
        earliestTime: '12:30 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR'
      };
    }

    // Set the selected date from the input if provided
    if (this.initialSelectedDate) {
      this.selectedDate = this.initialSelectedDate;
    }

    // Set the selected tab based on appointment type
    this.selectedSegment = this.appointmentType;

    // Generate time slots based on doctor's availability
    this.generateTimeSlots();
  }

  selectSegment(segment: string) {
    this.selectedSegment = segment;
  }

  selectDate(date: string) {
    this.selectedDate = date;
    this.generateTimeSlots();
  }

  selectTime(time: string) {
    this.selectedTime = time;
  }

  selectTab(tab: string | number | undefined) {
    if (tab && typeof tab === 'string') {
      this.selectedTab = tab;
      this.appointmentType = tab; // Keep appointmentType in sync
    }
  }

  generateTimeSlots() {
    // Mock time slots generation based on selected date
    const baseAfternoonSlots = ['12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM'];
    const baseEveningSlots = ['5:00 PM', '5:30 PM', '6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM', '8:30 PM'];

    // For demo purposes, make some slots unavailable
    this.afternoonSlots = baseAfternoonSlots.map(time => ({
      time,
      disabled: Math.random() > 0.7 // Randomly disable some slots
    }));

    this.eveningSlots = baseEveningSlots.map(time => ({
      time,
      disabled: Math.random() > 0.8 // Randomly disable some slots
    }));

    // Ensure at least one slot is available
    if (this.afternoonSlots.every(slot => slot.disabled)) {
      this.afternoonSlots[0].disabled = false;
    }
    if (this.eveningSlots.every(slot => slot.disabled)) {
      this.eveningSlots[0].disabled = false;
    }
  }

  async bookAppointment() {
    // If we have a saved payment method from previous navigation, use it directly
    if (this.savedPaymentMethod) {
      this.router.navigate(['/appointment-confirm'], {
        state: {
          doctor: this.doctor,
          appointmentType: this.selectedTab,
          selectedDate: this.selectedDate,
          selectedTime: this.selectedTime,
          paymentMethod: this.savedPaymentMethod
        }
      });
      return;
    }

    // Otherwise show payment option modal as bottom sheet
    const paymentModal = await this.modalController.create({
      component: PaymentOptionModalComponent,
      cssClass: 'payment-option-modal',
      backdropDismiss: true,
      breakpoints: [0, 0.4, 0.6],
      initialBreakpoint: 0.4,
      handle: false
    });

    paymentModal.onDidDismiss().then((result) => {
      if (result.data && result.data.paymentMethod) {
        // Navigate to appointment confirmation page
        this.router.navigate(['/appointment-confirm'], {
          state: {
            doctor: this.doctor,
            appointmentType: this.selectedTab,
            selectedDate: this.selectedDate,
            selectedTime: this.selectedTime,
            paymentMethod: result.data.paymentMethod
          }
        });
      }
    });

    await paymentModal.present();
  }

  goBack() {
    this.router.navigate(['/book-appointment'], {
      state: {
        selectedDate: this.selectedDate,
        appointmentType: this.appointmentType
      }
    });
  }
}
