import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonToggle,
  ModalController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { chevronBack } from 'ionicons/icons';

interface Doctor {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  ratingCount: number;
  languages: string;
  earliestTime: string;
  avatar: string;
}

@Component({
  selector: 'app-appointment-confirmation',
  templateUrl: './appointment-confirmation.component.html',
  styleUrls: ['./appointment-confirmation.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonToggle
  ]
})
export class AppointmentConfirmationComponent implements OnInit {
  @Input() doctor!: Doctor;
  @Input() appointmentType: string = 'online';
  @Input() selectedDate: string = '';
  @Input() selectedTime: string = '';
  @Input() paymentMethod: string = '';

  // Toggle state for payment method
  isInsuranceSelected: boolean = false;

  constructor(private modalController: ModalController) {
    addIcons({
      chevronBack
    });
  }

  ngOnInit() {
    // Set toggle state based on payment method
    this.isInsuranceSelected = this.paymentMethod === 'insurance';
  }

  get formattedDate(): string {
    // Convert date format from "30/1" to "Tuesday 30 Jan 2025, 6:30 PM"
    if (!this.selectedDate || !this.selectedTime) {
      return 'Date and time not selected';
    }

    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Parse the selectedDate format "30/1" (day/month)
    const [day, month] = this.selectedDate.split('/').map(num => parseInt(num));

    // Create date object for the current year (2025)
    const currentYear = new Date().getFullYear();
    const appointmentDate = new Date(currentYear, month - 1, day); // month is 0-indexed

    const dayName = days[appointmentDate.getDay()];
    const monthName = months[appointmentDate.getMonth()];

    return `${dayName} ${day} ${monthName} ${currentYear}, ${this.selectedTime}`;
  }

  get consultationFee(): string {
    return '99 AED';
  }

  dismiss() {
    this.modalController.dismiss();
  }

  selectPaymentMethod(method: string) {
    this.paymentMethod = method;
    this.isInsuranceSelected = method === 'insurance';
  }

  confirmAppointment() {
    // Handle appointment confirmation
    this.modalController.dismiss({
      confirmed: true,
      appointmentData: {
        doctor: this.doctor,
        appointmentType: this.appointmentType,
        date: this.selectedDate,
        time: this.selectedTime,
        paymentMethod: this.paymentMethod
      }
    });
  }
}
