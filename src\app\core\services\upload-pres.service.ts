import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { URLS } from '../constants/index';

@Injectable({
  providedIn: 'root',
})
export class UploadPresService {
  constructor(private httpClient: HttpClient) {}

  getUploadedPrescription(id: any):Observable<any> {
    return this.httpClient.get<any>(
      URLS.urlWithParams(URLS.GET_UPLOADED_PRESCRIPTION, [
        { key: 'id', value: id },
      ])
    );
  }
  getMedicalFacilities():Observable<any> {
    return this.httpClient.get<any>(URLS.MEDICAL_FACILITY);
  }
  getPatientList():Observable<any>{
    return this.httpClient.get<any>(URLS.PATIENT_LIST);
  }
  UploadPrescription(Details: any, tab: any): Observable<any> {
    if (tab === 3) {
      return this.httpClient.post<any>(URLS.DUBAI_UPLOAD_PRESCRIPTION, Details);
    } else {
      return this.httpClient.post<any>(URLS.UPLOAD_PRESCRIPTION, Details);
    }
  }
  getInsuranceTypeList():Observable<any> {
    return this.httpClient.get<any>(URLS.INSURANCER_TYPES);
  }
  editUploadedPrescription(payload: any): Observable<any> {
    return this.httpClient.post<any>(URLS.EDIT_UPLOADED_PRESCRIPTION, payload);
  }
}
