{"doctors": [{"id": 3, "name": "Dr. <PERSON>", "specialty": "General Physician", "rating": 4.8, "ratingCount": 35, "languages": "English, Arabic", "earliestTime": "10:00 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM"], "evening": ["6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "1/2": {"afternoon": ["11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "2/2": {"afternoon": ["9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 8, "name": "Dr. <PERSON>", "specialty": "General Physician", "rating": 4.5, "ratingCount": 29, "languages": "English, Arabic", "earliestTime": "3:15 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": [], "evening": ["7:30 PM", "8:00 PM", "8:30 PM"]}, "31/1": {"afternoon": ["3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "1/2": {"afternoon": ["2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 9, "name": "Dr. <PERSON>", "specialty": "General Physician", "rating": 4.7, "ratingCount": 42, "languages": "English, Arabic, Urdu", "earliestTime": "8:45 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["8:45 AM", "9:15 AM", "9:45 AM", "10:15 AM", "10:45 AM", "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:30 PM", "2:00 PM"], "evening": []}, "31/1": {"afternoon": ["9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM"], "evening": ["6:45 PM", "7:15 PM", "7:45 PM"]}, "1/2": {"afternoon": ["8:30 AM", "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM"], "evening": ["7:15 PM", "7:45 PM"]}, "2/2": {"afternoon": ["9:15 AM", "9:45 AM", "10:15 AM", "10:45 AM", "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:00 PM", "2:30 PM"], "evening": []}}}, {"id": 4, "name": "Dr. <PERSON>", "specialty": "Cardiologist", "rating": 4.9, "ratingCount": 52, "languages": "English, Arabic", "earliestTime": "2:30 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "1/2": {"afternoon": ["2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "2/2": {"afternoon": ["2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 10, "name": "Dr. <PERSON><PERSON>", "specialty": "Cardiologist", "rating": 4.6, "ratingCount": 38, "languages": "English, Arabic, French", "earliestTime": "11:00 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 11, "name": "Dr. <PERSON>", "specialty": "Cardiologist", "rating": 4.8, "ratingCount": 47, "languages": "English, Arabic", "earliestTime": "4:00 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["4:00 PM", "4:30 PM", "5:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "1/2": {"afternoon": ["4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "2/2": {"afternoon": ["4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 5, "name": "Dr. <PERSON><PERSON>", "specialty": "Dermatologist", "rating": 4.7, "ratingCount": 28, "languages": "English, Arabic, French", "earliestTime": "11:15 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "1/2": {"afternoon": ["11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:15 PM", "2:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "2/2": {"afternoon": ["11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:15 PM", "2:45 PM", "3:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}}}, {"id": 12, "name": "Dr. <PERSON><PERSON><PERSON>", "specialty": "Dermatologist", "rating": 4.9, "ratingCount": 51, "languages": "English, Arabic", "earliestTime": "9:00 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 13, "name": "Dr. <PERSON><PERSON>", "specialty": "Dermatologist", "rating": 4.4, "ratingCount": 33, "languages": "English, Arabic, German", "earliestTime": "1:30 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 6, "name": "Dr. <PERSON>", "specialty": "Pediatrician", "rating": 4.6, "ratingCount": 41, "languages": "English, Arabic", "earliestTime": "9:30 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 14, "name": "Dr. <PERSON>", "specialty": "Pediatrician", "rating": 4.8, "ratingCount": 36, "languages": "English, Arabic, French", "earliestTime": "10:45 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["10:45 AM", "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM", "5:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["10:45 AM", "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["10:45 AM", "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["10:45 AM", "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "1:15 PM", "1:45 PM", "2:15 PM", "2:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 15, "name": "Dr. <PERSON><PERSON><PERSON>", "specialty": "Pediatrician", "rating": 4.7, "ratingCount": 44, "languages": "English, Arabic", "earliestTime": "2:15 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM", "5:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 7, "name": "Dr. <PERSON>", "specialty": "Orthopedic", "rating": 4.8, "ratingCount": 33, "languages": "English, Arabic, German", "earliestTime": "1:45 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["1:45 PM", "2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM", "5:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["1:45 PM", "2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["1:45 PM", "2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["1:45 PM", "2:15 PM", "2:45 PM", "3:15 PM", "3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 16, "name": "Dr. <PERSON>", "specialty": "Orthopedic", "rating": 4.5, "ratingCount": 27, "languages": "English, Arabic", "earliestTime": "8:30 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["8:30 AM", "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["8:30 AM", "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["8:30 AM", "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["8:30 AM", "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 17, "name": "Dr. <PERSON><PERSON>", "specialty": "Orthopedic", "rating": 4.9, "ratingCount": 39, "languages": "English, Arabic, Persian", "earliestTime": "3:45 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM", "5:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["3:45 PM", "4:15 PM", "4:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["3:45 PM", "4:15 PM", "4:45 PM", "5:15 PM", "5:45 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 18, "name": "Dr. <PERSON><PERSON><PERSON>", "specialty": "Neurologist", "rating": 4.7, "ratingCount": 31, "languages": "English, Arabic", "earliestTime": "10:30 AM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 19, "name": "Dr. <PERSON><PERSON>", "specialty": "Neurologist", "rating": 4.8, "ratingCount": 45, "languages": "English, Arabic, French", "earliestTime": "12:00 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM", "4:00 PM", "4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["12:00 PM", "12:30 PM", "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}, {"id": 20, "name": "Dr. <PERSON><PERSON><PERSON>", "specialty": "Neurologist", "rating": 4.6, "ratingCount": 37, "languages": "English, Arabic", "earliestTime": "4:30 PM", "avatar": "https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR", "availability": {"30/1": {"afternoon": ["4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}, "31/1": {"afternoon": ["4:30 PM", "5:00 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM"]}, "1/2": {"afternoon": ["4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM"]}, "2/2": {"afternoon": ["4:30 PM", "5:00 PM", "5:30 PM"], "evening": ["6:00 PM", "6:30 PM", "7:00 PM", "7:30 PM", "8:00 PM"]}}}]}