import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';
@Injectable({
  providedIn: 'root',
})
export class ToastService {
  constructor(public toastCtrl: ToastController) {}
  async presentToast(text: any) {
    let toast = await this.toastCtrl.create({
      message: text,
      duration: 3000,
      position: 'bottom',
      mode: 'md',
    });
    toast.present();
  }
}
