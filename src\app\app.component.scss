// App container styles
.app-container {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(180deg, #023753 0%, #020621 100%);
}

// Global ion-app styling with gradient background
ion-app {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
  --color: #ffffff !important;
  background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
  height: 100vh !important;
  width: 100vw !important;
}

// Global ion-content styling
ion-content {
  --background: transparent !important;
  --color: #ffffff !important;
  background: transparent !important;
}

// Global body and html styling
:root {
  --ion-background-color: linear-gradient(180deg, #023753 0%, #020621 100%);
}

body {
  background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
}

html {
  background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
}
