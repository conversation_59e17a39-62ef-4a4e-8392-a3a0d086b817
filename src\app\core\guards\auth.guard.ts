import { Injectable, inject } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { of, map } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  private router = inject(Router);
  canActivate() {
    return of({ user: 'mock' }).pipe(
      map((auth: { user: string } | null) => {
        console.log(auth);
        if (auth !== null) {
          console.log(auth);
          return true;
        }
        return this.router.createUrlTree(['']);
      })
    );
  }
}
