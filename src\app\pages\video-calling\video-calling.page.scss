// Video Calling Page Styles
.video-call-content {
  --background: #031934;
  --color: #ffffff;
  position: relative;
  height: 100vh;
  overflow: hidden;
  padding: 0;
  padding-top: 28px;
}

// Header with back button and doctor info
.video-header {
  position: absolute;
  top: 28px;
  left: 0;
  width: 100%;
  height: 60px;
  background: #031934;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
  border-radius: 0 0 16px 16px;

  .back-button {
    --color: #ffffff;
    --background: transparent;
    margin: 0;

    ion-icon {
      font-size: 24px;
    }
  }

  .doctor-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .doctor-name {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }

    .connection-indicator {
      width: 8px;
      height: 8px;
      background: #ff4444;
      border-radius: 50%;
      transition: background-color 0.3s ease;

      &.connected {
        background: #28a745;
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.6);
      }
    }
  }

  .camera-switch-button {
    --color: #ffffff;
    --background: transparent;
    margin: 0;

    ion-icon {
      font-size: 24px;
    }
  }
}

// Main Video Container
.main-video-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 28px);
  background: #000000;
  margin-top: 28px;

  .remote-video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000000;
    z-index: 1;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .doctor-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);

      .doctor-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: url('https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=600&fit=crop&crop=face') center/cover;
      }
    }
  }

  .local-video-container {
    position: absolute;
    top: 72px;
    right: 16px;
    width: 120px;
    height: 160px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    overflow: hidden;
    z-index: 20;
    cursor: grab;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);

    &:active {
      cursor: grabbing;
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .patient-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #6c757d 0%, #495057 100%);

      .patient-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=300&fit=crop&crop=face') center/cover;
      }
    }
  }


}

// Call Controls
.call-controls-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 30;
  padding: 32px 24px;
  background: #031934;
  border-radius: 16px 16px 0 0;

  .call-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;

    .control-button {
      --background: rgba(255, 255, 255, 0.15);
      --background-activated: rgba(255, 255, 255, 0.25);
      --background-hover: rgba(255, 255, 255, 0.2);
      --color: #ffffff;
      --border-radius: 50%;
      width: 56px;
      height: 56px;
      margin: 0;

      &.active {
        --background: rgba(255, 255, 255, 0.25);
        --color: #ffffff;
      }

      ion-icon {
        font-size: 24px;
      }
    }

    .end-call-button {
      --background: #ff4444;
      --background-activated: #cc3333;
      --background-hover: #dd3333;
      --color: #ffffff;
      --border-radius: 50%;
      width: 56px;
      height: 56px;
      margin: 0;

      ion-icon {
        font-size: 24px;
        transform: rotate(135deg);
      }
    }
  }
}



// Responsive adjustments
@media (max-width: 480px) {
  .video-header {
    height: 56px;
    padding: 0 12px;
    top: 28px;

    .doctor-name {
      font-size: 16px;
    }

    .back-button ion-icon,
    .camera-switch-button ion-icon {
      font-size: 20px;
    }
  }

  .main-video-container {
    height: calc(100vh - 28px);
    margin-top: 28px;

    .local-video-container {
      width: 100px;
      height: 140px;
      top: 64px;
      right: 12px;
      border-radius: 12px;
    }
  }

  .call-controls-container {
    padding: 24px 16px;

    .call-controls {
      gap: 20px;

      .control-button {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 20px;
        }
      }

      .end-call-button {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 20px;
        }
      }
    }
  }
}