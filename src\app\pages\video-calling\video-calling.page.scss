// Video Calling Page Styles
.video-call-header {
  --background: #031934;
  --color: #ffffff;

  .video-call-toolbar {
    --background: #031934;
    --color: #ffffff;
    --border-color: transparent;

    .video-call-title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }

    ion-button {
      --color: #ffffff;

      ion-icon {
        font-size: 24px;
      }
    }
  }
}

.video-call-content {
  --background: #000000;
  --color: #ffffff;
  position: relative;
  height: 100vh;
  overflow: hidden;
}

// Connection Container (when not connected)
.connection-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #031934 0%, #0a2a4a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .connection-content {
    text-align: center;
    padding: 32px 24px;
    max-width: 400px;

    .connection-animation {
      margin-bottom: 32px;

      ion-spinner {
        width: 60px;
        height: 60px;
        --color: #ffffff;
      }
    }

    .connection-title {
      color: #ffffff;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 16px;
      line-height: 1.3;
    }

    .connection-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      font-weight: 400;
      line-height: 1.4;
      margin-bottom: 32px;
    }

    .manual-join-buttons {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .join-button {
        --background: rgba(255, 255, 255, 0.1);
        --background-activated: rgba(255, 255, 255, 0.2);
        --background-hover: rgba(255, 255, 255, 0.15);
        --color: #ffffff;
        --border-radius: 12px;
        height: 48px;
        font-weight: 500;

        &.patient-button {
          --background: #007bff;
          --background-activated: #0056b3;
          --background-hover: #0069d9;
        }

        &.doctor-button {
          --background: #28a745;
          --background-activated: #1e7e34;
          --background-hover: #218838;
        }

        ion-spinner {
          margin-right: 8px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

// Video Call Container (when connected)
.video-call-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000000;

  .remote-video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000000;
    z-index: 1;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .local-video-container {
    position: absolute;
    top: 100px;
    right: 16px;
    width: 86px;
    height: 130px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    overflow: hidden;
    z-index: 20;
    cursor: grab;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);

    &:active {
      cursor: grabbing;
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .connection-status {
    position: absolute;
    top: 80px;
    left: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 0, 0.6);
    padding: 8px 12px;
    border-radius: 20px;
    z-index: 15;

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;

      &.connected {
        background: #28a745;
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.6);
      }
    }

    .status-text {
      color: #ffffff;
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// Device Settings Panel
.device-settings-panel {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 40;
  width: 320px;
  max-width: 90vw;

  .settings-content {
    background: rgba(3, 25, 52, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);

    .settings-title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }

    .settings-section {
      margin-bottom: 16px;

      .settings-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;
        display: block;
      }

      .speaker-list {
        max-height: 160px;
        overflow-y: auto;
        margin-bottom: 16px;

        ion-radio-group {
          .speaker-item {
            --background: transparent;
            --color: #ffffff;
            --border-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 8px;
            border-radius: 8px;

            ion-radio {
              --color: #007bff;
              --color-checked: #007bff;
            }

            .speaker-label {
              color: #ffffff;
              font-size: 14px;
            }
          }
        }

        .no-speakers {
          color: rgba(255, 255, 255, 0.6);
          font-size: 14px;
          text-align: center;
          padding: 16px;
        }
      }

      .speaker-toggle {
        --background: transparent;
        --color: #ffffff;
        --border-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;

        ion-checkbox {
          --color: #007bff;
          --color-checked: #007bff;
        }

        .toggle-label {
          color: #ffffff;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .close-settings-button {
      --color: #ffffff;
      --background: rgba(255, 255, 255, 0.1);
      --background-hover: rgba(255, 255, 255, 0.2);
      --border-radius: 8px;
      height: 40px;
      font-weight: 500;
    }
  }
}

// Call Controls
.call-controls-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 30;
  padding: 24px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));

  .call-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    padding: 12px 16px;

    .control-button {
      --background: rgba(255, 255, 255, 0.1);
      --background-activated: rgba(255, 255, 255, 0.2);
      --background-hover: rgba(255, 255, 255, 0.15);
      --color: #ffffff;
      --border-radius: 50%;
      width: 56px;
      height: 56px;
      margin: 0;

      &.active {
        --background: rgba(255, 255, 255, 0.2);
        --color: #ffffff;
      }

      ion-icon {
        font-size: 24px;
      }
    }

    .end-call-button {
      --background: #dc3545;
      --background-activated: #c82333;
      --background-hover: #bd2130;
      --color: #ffffff;
      --border-radius: 50%;
      width: 64px;
      height: 64px;
      margin: 0;

      ion-icon {
        font-size: 28px;
        transform: rotate(135deg);
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .device-settings-panel {
    width: 280px;

    .settings-content {
      padding: 20px;
    }
  }

  .call-controls-container {
    padding: 20px 16px;

    .call-controls {
      gap: 12px;
      padding: 10px 12px;

      .control-button {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 20px;
        }
      }

      .end-call-button {
        width: 56px;
        height: 56px;

        ion-icon {
          font-size: 24px;
        }
      }
    }
  }
}