// Video Calling Page Styles
.video-call-content {
  --background: #031934;
  --color: #ffffff;
  position: relative;
  height: 100vh;
  overflow: hidden;
  padding: 0;
  padding-top: 28px;
}

// Header with back button and doctor info
.video-header {
  position: absolute;
  top: 28px;
  left: 0;
  width: 100%;
  height: 60px;
  background: #031934;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
  border-radius: 0 0 16px 16px;

  .back-button {
    --color: #ffffff;
    --background: transparent;
    margin: 0;

    ion-icon {
      font-size: 24px;
    }
  }

  .doctor-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .doctor-name {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }

    .connection-indicator {
      width: 8px;
      height: 8px;
      background: #ff4444;
      border-radius: 50%;
      transition: background-color 0.3s ease;

      &.connected {
        background: #28a745;
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.6);
      }
    }
  }

  .camera-switch-button {
    --color: #ffffff;
    --background: transparent;
    margin: 0;

    ion-icon {
      font-size: 24px;
    }
  }
}

// Connection Container (when not connected)
.connection-container {
  position: absolute;
  top: 88px;
  left: 0;
  width: 100%;
  height: calc(100vh - 88px - 120px);
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .connection-content {
    text-align: center;
    padding: 32px 24px;
    max-width: 400px;

    .connection-animation {
      margin-bottom: 32px;

      ion-spinner {
        width: 60px;
        height: 60px;
        --color: #ffffff;
      }
    }

    .connection-title {
      color: #ffffff;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 16px;
      line-height: 1.3;
    }

    .connection-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      font-weight: 400;
      line-height: 1.4;
      margin-bottom: 32px;
    }

    .thank-you-actions {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 24px;
      margin-top: 32px;

      .thank-you-icon {
        ion-icon {
          font-size: 64px;
          color: #28a745;
        }
      }

      .thank-you-buttons {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;
        max-width: 300px;

        .action-button {
          --border-radius: 12px;
          height: 48px;
          font-weight: 500;

          &.primary-button {
            --background: #007bff;
            --background-activated: #0056b3;
            --background-hover: #0069d9;
            --color: #ffffff;
          }

          &.secondary-button {
            --background: transparent;
            --background-activated: rgba(255, 255, 255, 0.1);
            --background-hover: rgba(255, 255, 255, 0.05);
            --color: #ffffff;
            --border-color: rgba(255, 255, 255, 0.3);
            --border-width: 1px;
            --border-style: solid;
          }

          ion-icon {
            margin-right: 8px;
          }
        }
      }
    }

    .manual-join-buttons {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .join-button {
        --background: rgba(255, 255, 255, 0.1);
        --background-activated: rgba(255, 255, 255, 0.2);
        --background-hover: rgba(255, 255, 255, 0.15);
        --color: #ffffff;
        --border-radius: 12px;
        height: 48px;
        font-weight: 500;

        &.patient-button {
          --background: #007bff;
          --background-activated: #0056b3;
          --background-hover: #0069d9;
        }

        &.doctor-button {
          --background: #28a745;
          --background-activated: #1e7e34;
          --background-hover: #218838;
        }

        ion-spinner {
          margin-right: 8px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

// Main Video Container (when connected)
.main-video-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 28px);
  background: #000000;
  margin-top: 28px;

  .remote-video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000000;
    z-index: 1;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .local-video-container {
    position: absolute;
    top: 72px;
    right: 16px;
    width: 120px;
    height: 160px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    overflow: hidden;
    z-index: 20;
    cursor: grab;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);

    &:active {
      cursor: grabbing;
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }


}

// Call Controls
.call-controls-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 30;
  padding: 32px 24px;
  background: #031934;
  border-radius: 16px 16px 0 0;

  .call-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;

    .control-button {
      --background: rgba(255, 255, 255, 0.15);
      --background-activated: rgba(255, 255, 255, 0.25);
      --background-hover: rgba(255, 255, 255, 0.2);
      --color: #ffffff;
      --border-radius: 50%;
      width: 56px;
      height: 56px;
      margin: 0;

      &.active {
        --background: rgba(255, 255, 255, 0.25);
        --color: #ffffff;
      }

      ion-icon {
        font-size: 24px;
      }
    }

    .end-call-button {
      --background: #ff4444;
      --background-activated: #cc3333;
      --background-hover: #dd3333;
      --color: #ffffff;
      --border-radius: 50%;
      width: 56px;
      height: 56px;
      margin: 0;

      ion-icon {
        font-size: 24px;
        transform: rotate(135deg);
      }
    }
  }
}



// Responsive adjustments
@media (max-width: 480px) {
  .video-header {
    height: 56px;
    padding: 0 12px;
    top: 28px;

    .doctor-name {
      font-size: 16px;
    }

    .back-button ion-icon,
    .camera-switch-button ion-icon {
      font-size: 20px;
    }
  }

  .connection-container {
    top: 84px;
    height: calc(100vh - 84px - 100px);

    .connection-content {
      padding: 24px 16px;

      .connection-title {
        font-size: 20px;
      }

      .connection-subtitle {
        font-size: 14px;
      }

      .thank-you-actions {
        margin-top: 24px;

        .thank-you-icon ion-icon {
          font-size: 48px;
        }

        .thank-you-buttons {
          max-width: 280px;

          .action-button {
            height: 44px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .main-video-container {
    height: calc(100vh - 28px);
    margin-top: 28px;

    .local-video-container {
      width: 100px;
      height: 140px;
      top: 64px;
      right: 12px;
      border-radius: 12px;
    }
  }

  .call-controls-container {
    padding: 24px 16px;

    .call-controls {
      gap: 20px;

      .control-button {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 20px;
        }
      }

      .end-call-button {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 20px;
        }
      }
    }
  }
}