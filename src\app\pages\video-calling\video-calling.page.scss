// Video Calling Page Styles
.video-call-content {
  --background: transparent;
  --color: #ffffff;
  position: relative;
  height: 100vh;
  overflow: hidden;
  padding: 0;
}

// Header with back button and doctor info
.video-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background: #031934;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;

  .back-button {
    --color: #ffffff;
    --background: transparent;
    margin: 0;

    ion-icon {
      font-size: 24px;
    }
  }

  .doctor-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .doctor-name {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }

    .connection-indicator {
      width: 8px;
      height: 8px;
      background: #ff4444;
      border-radius: 50%;
    }
  }

  .camera-switch-button {
    --color: #ffffff;
    --background: transparent;
    margin: 0;

    ion-icon {
      font-size: 24px;
    }
  }
}

// Main Video Container
.main-video-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000000;

  .remote-video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000000;
    z-index: 1;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .doctor-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);

      .doctor-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: url('https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=600&fit=crop&crop=face') center/cover;
      }
    }
  }

  .local-video-container {
    position: absolute;
    top: 100px;
    right: 16px;
    width: 120px;
    height: 160px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    overflow: hidden;
    z-index: 20;
    cursor: grab;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);

    &:active {
      cursor: grabbing;
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .patient-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #6c757d 0%, #495057 100%);

      .patient-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=300&fit=crop&crop=face') center/cover;
      }
    }
  }

  .recording-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 15;

    .recording-dot {
      width: 12px;
      height: 12px;
      background: #ff4444;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
  }
}

// Call Controls
.call-controls-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 30;
  padding: 32px 24px;
  background: #031934;

  .call-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;

    .control-button {
      --background: rgba(255, 255, 255, 0.15);
      --background-activated: rgba(255, 255, 255, 0.25);
      --background-hover: rgba(255, 255, 255, 0.2);
      --color: #ffffff;
      --border-radius: 50%;
      width: 56px;
      height: 56px;
      margin: 0;

      &.active {
        --background: rgba(255, 255, 255, 0.25);
        --color: #ffffff;
      }

      ion-icon {
        font-size: 24px;
      }
    }

    .end-call-button {
      --background: #ff4444;
      --background-activated: #cc3333;
      --background-hover: #dd3333;
      --color: #ffffff;
      --border-radius: 50%;
      width: 64px;
      height: 64px;
      margin: 0;

      ion-icon {
        font-size: 28px;
        transform: rotate(135deg);
      }
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .video-header {
    height: 56px;
    padding: 0 12px;

    .doctor-name {
      font-size: 16px;
    }

    .back-button ion-icon,
    .camera-switch-button ion-icon {
      font-size: 20px;
    }
  }

  .main-video-container {
    .local-video-container {
      width: 100px;
      height: 140px;
      top: 80px;
      right: 12px;
      border-radius: 12px;
    }
  }

  .call-controls-container {
    padding: 24px 16px;

    .call-controls {
      gap: 20px;

      .control-button {
        width: 48px;
        height: 48px;

        ion-icon {
          font-size: 20px;
        }
      }

      .end-call-button {
        width: 56px;
        height: 56px;

        ion-icon {
          font-size: 24px;
        }
      }
    }
  }
}