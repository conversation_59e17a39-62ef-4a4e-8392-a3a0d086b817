import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonSearchbar,
  IonIcon
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { language, star, chevronForward } from 'ionicons/icons';

export interface Doctor {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  ratingCount: number;
  languages: string;
  earliestTime: string;
  avatar: string;
  availability?: {
    [date: string]: {
      afternoon: string[];
      evening: string[];
    };
  };
}

@Component({
  selector: 'app-in-person-doctors',
  templateUrl: './in-person-doctors.component.html',
  styleUrls: ['./in-person-doctors.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonSearchbar,
    IonIcon
  ]
})
export class InPersonDoctorsComponent implements OnInit, OnChanges {
  @Input() selectedFacility: string = '';
  @Input() selectedSpecialty: string = '';
  @Input() selectedDate: string = '';
  @Output() doctorSelected = new EventEmitter<Doctor>();

  searchTerm: string = '';
  allDoctors: Doctor[] = [];
  filteredDoctors: Doctor[] = [];

  constructor() {
    addIcons({
      language,
      star,
      chevronForward
    });
  }

  ngOnInit() {
    this.initializeDoctors();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['selectedSpecialty'] || changes['selectedFacility']) {
      this.filterDoctors();
    }
    if (changes['selectedDate']) {
      this.updateEarliestTimes();
    }
  }

  updateEarliestTimes() {
    this.allDoctors.forEach(doctor => {
      if (doctor.availability && doctor.availability[this.selectedDate]) {
        const dayAvailability = doctor.availability[this.selectedDate];
        const allTimes = [...dayAvailability.afternoon, ...dayAvailability.evening];
        if (allTimes.length > 0) {
          // Sort times and get the earliest
          const sortedTimes = allTimes.sort((a, b) => {
            const timeA = this.convertTimeToMinutes(a);
            const timeB = this.convertTimeToMinutes(b);
            return timeA - timeB;
          });
          doctor.earliestTime = sortedTimes[0];
        } else {
          doctor.earliestTime = 'No availability';
        }
      }
    });
    this.filterDoctors();
  }

  convertTimeToMinutes(time: string): number {
    const [timePart, period] = time.split(' ');
    const [hours, minutes] = timePart.split(':').map(Number);
    let totalMinutes = hours * 60 + (minutes || 0);

    if (period === 'PM' && hours !== 12) {
      totalMinutes += 12 * 60;
    } else if (period === 'AM' && hours === 12) {
      totalMinutes -= 12 * 60;
    }

    return totalMinutes;
  }

  initializeDoctors() {
    this.allDoctors = [
      // General Physician (3 doctors)
      {
        id: 3,
        name: 'Dr. Sarah Ahmed',
        specialty: 'General Physician',
        rating: 4.8,
        ratingCount: 35,
        languages: 'English, Arabic',
        earliestTime: '10:00 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM'], evening: ['6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '1/2': { afternoon: ['11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '2/2': { afternoon: ['9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 8,
        name: 'Dr. Omar Hassan',
        specialty: 'General Physician',
        rating: 4.5,
        ratingCount: 29,
        languages: 'English, Arabic',
        earliestTime: '3:15 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: [], evening: ['7:30 PM', '8:00 PM', '8:30 PM'] },
          '31/1': { afternoon: ['3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '1/2': { afternoon: ['2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 9,
        name: 'Dr. Nadia Al-Rashid',
        specialty: 'General Physician',
        rating: 4.7,
        ratingCount: 42,
        languages: 'English, Arabic, Urdu',
        earliestTime: '8:45 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['8:45 AM', '9:15 AM', '9:45 AM', '10:15 AM', '10:45 AM', '11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:30 PM', '2:00 PM'], evening: [] },
          '31/1': { afternoon: ['9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM'], evening: ['6:45 PM', '7:15 PM', '7:45 PM'] },
          '1/2': { afternoon: ['8:30 AM', '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM'], evening: ['7:15 PM', '7:45 PM'] },
          '2/2': { afternoon: ['9:15 AM', '9:45 AM', '10:15 AM', '10:45 AM', '11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:00 PM', '2:30 PM'], evening: [] }
        }
      },
      // Cardiologist (3 doctors)
      {
        id: 4,
        name: 'Dr. Mohammed Ali',
        specialty: 'Cardiologist',
        rating: 4.9,
        ratingCount: 52,
        languages: 'English, Arabic',
        earliestTime: '2:30 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '1/2': { afternoon: ['2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '2/2': { afternoon: ['2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 10,
        name: 'Dr. Amira Farouk',
        specialty: 'Cardiologist',
        rating: 4.6,
        ratingCount: 38,
        languages: 'English, Arabic, French',
        earliestTime: '11:00 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 11,
        name: 'Dr. Khalid Mahmoud',
        specialty: 'Cardiologist',
        rating: 4.8,
        ratingCount: 47,
        languages: 'English, Arabic',
        earliestTime: '4:00 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['4:00 PM', '4:30 PM', '5:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '1/2': { afternoon: ['4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '2/2': { afternoon: ['4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      // Dermatologist (3 doctors)
      {
        id: 5,
        name: 'Dr. Fatima Hassan',
        specialty: 'Dermatologist',
        rating: 4.7,
        ratingCount: 28,
        languages: 'English, Arabic, French',
        earliestTime: '11:15 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '1/2': { afternoon: ['11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:15 PM', '2:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '2/2': { afternoon: ['11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:15 PM', '2:45 PM', '3:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] }
        }
      },
      {
        id: 12,
        name: 'Dr. James Vowles',
        specialty: 'Dermatologist',
        rating: 4.9,
        ratingCount: 51,
        languages: 'English, Arabic',
        earliestTime: '9:00 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 13,
        name: 'Dr. Rami Saleh',
        specialty: 'Dermatologist',
        rating: 4.4,
        ratingCount: 33,
        languages: 'English, Arabic, German',
        earliestTime: '1:30 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      // Pediatrician (3 doctors)
      {
        id: 6,
        name: 'Dr. Ahmed Khalil',
        specialty: 'Pediatrician',
        rating: 4.6,
        ratingCount: 41,
        languages: 'English, Arabic',
        earliestTime: '9:30 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 14,
        name: 'Dr. Mona El-Shamy',
        specialty: 'Pediatrician',
        rating: 4.8,
        ratingCount: 36,
        languages: 'English, Arabic, French',
        earliestTime: '10:45 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['10:45 AM', '11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM', '5:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['10:45 AM', '11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['10:45 AM', '11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['10:45 AM', '11:15 AM', '11:45 AM', '12:15 PM', '12:45 PM', '1:15 PM', '1:45 PM', '2:15 PM', '2:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 15,
        name: 'Dr. Tariq Al-Zahra',
        specialty: 'Pediatrician',
        rating: 4.7,
        ratingCount: 44,
        languages: 'English, Arabic',
        earliestTime: '2:15 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM', '5:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      // Orthopedic (3 doctors)
      {
        id: 7,
        name: 'Dr. Layla Mansour',
        specialty: 'Orthopedic',
        rating: 4.8,
        ratingCount: 33,
        languages: 'English, Arabic, German',
        earliestTime: '1:45 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['1:45 PM', '2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM', '5:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['1:45 PM', '2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['1:45 PM', '2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['1:45 PM', '2:15 PM', '2:45 PM', '3:15 PM', '3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 16,
        name: 'Dr. Hassan Al-Mutairi',
        specialty: 'Orthopedic',
        rating: 4.5,
        ratingCount: 27,
        languages: 'English, Arabic',
        earliestTime: '8:30 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['8:30 AM', '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['8:30 AM', '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['8:30 AM', '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['8:30 AM', '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 17,
        name: 'Dr. Samira Qasemi',
        specialty: 'Orthopedic',
        rating: 4.9,
        ratingCount: 39,
        languages: 'English, Arabic, Persian',
        earliestTime: '3:45 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM', '5:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['3:45 PM', '4:15 PM', '4:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['3:45 PM', '4:15 PM', '4:45 PM', '5:15 PM', '5:45 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      // Neurologist (3 doctors)
      {
        id: 18,
        name: 'Dr. Adnan Khoury',
        specialty: 'Neurologist',
        rating: 4.7,
        ratingCount: 31,
        languages: 'English, Arabic',
        earliestTime: '10:30 AM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['10:30 AM', '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 19,
        name: 'Dr. Leila Badawi',
        specialty: 'Neurologist',
        rating: 4.8,
        ratingCount: 45,
        languages: 'English, Arabic, French',
        earliestTime: '12:00 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['12:00 PM', '12:30 PM', '1:00 PM', '1:30 PM', '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      },
      {
        id: 20,
        name: 'Dr. Faisal Al-Najjar',
        specialty: 'Neurologist',
        rating: 4.6,
        ratingCount: 37,
        languages: 'English, Arabic',
        earliestTime: '4:30 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR',
        availability: {
          '30/1': { afternoon: ['4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] },
          '31/1': { afternoon: ['4:30 PM', '5:00 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM'] },
          '1/2': { afternoon: ['4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM'] },
          '2/2': { afternoon: ['4:30 PM', '5:00 PM', '5:30 PM'], evening: ['6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM'] }
        }
      }
    ];
    this.updateEarliestTimes();
    this.filterDoctors();
  }

  shouldShowDoctors(): boolean {
    return this.selectedFacility !== 'Select Facility' && 
           this.selectedSpecialty !== 'Select Speciality';
  }

  filterDoctors() {
    if (!this.shouldShowDoctors()) {
      this.filteredDoctors = [];
      return;
    }

    // Filter by specialty first
    let doctors = this.allDoctors;
    if (this.selectedSpecialty !== 'Select Speciality') {
      doctors = this.allDoctors.filter(doctor => 
        doctor.specialty === this.selectedSpecialty
      );
    }

    // Then filter by search term
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.filteredDoctors = doctors;
    } else {
      const searchTerm = this.searchTerm.toLowerCase().trim();
      this.filteredDoctors = doctors.filter(doctor =>
        doctor.name.toLowerCase().includes(searchTerm) ||
        doctor.specialty.toLowerCase().includes(searchTerm) ||
        doctor.languages.toLowerCase().includes(searchTerm)
      );
    }
  }

  onSearchInput() {
    this.filterDoctors();
  }

  selectDoctor(doctor: Doctor) {
    // Emit the doctor selection event to parent component
    // The parent component will handle navigation to the doctor details page
    this.doctorSelected.emit(doctor);
  }
}
