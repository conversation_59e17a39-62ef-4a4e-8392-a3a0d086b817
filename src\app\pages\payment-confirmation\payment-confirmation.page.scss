// Payment Confirmation Page Styles
.payment-header {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  margin-top: 28px;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
  position: relative;

  // Black overlay from top of modal to bottom of toolbar
  &::before {
    content: '';
    position: absolute;
    top: -28px; // Extend up to cover the margin-top area
    left: 0;
    right: 0;
    bottom: 0; // This will go to the bottom of the header including toolbar
    background: rgba(0, 0, 0, 0.04);
    z-index: 0;
  }

  .payment-toolbar {
    --background: transparent;
    --color: #ffffff;
    --border-width: 0;
    --border-style: solid;
    --border-color: #16425d;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --padding-start: 12px;
    --padding-end: 16px;
    background: transparent;
    min-height: 56px;
    position: relative;
    z-index: 1;

    .payment-title {
      --color: #ffffff;
      font-size: 20px;
      font-weight: 500;
      padding: 0 0 0 0;
      margin-bottom: -6px;
    }

    ion-button {
      --color: #ffffff;
      --background: transparent;

      ion-icon {
        font-size: 24px;
      }
    }
  }
}

.payment-content {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  
  .payment-container {
    padding: 24px 16px 100px 16px; // Extra bottom padding for button
    
    // Appointment Date & Time
    .appointment-datetime {
      margin-bottom: 24px;
      
      .datetime-text {
        color: #ffffff;
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        line-height: 1.4;
        margin-top: -6px;
      }
    }

    // Doctor Info Section
    .doctor-info-section {
      margin-bottom: 15px;
      
      .specialty-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        font-weight: 400;
        margin-bottom: -2px;
      }
      
      .doctor-name-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .doctor-name {
          color: #ffffff;
          font-size: 16px;
          font-weight: 400;
          margin: 0;
        }
        
        .doctor-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    // Appointment Details
    .appointment-details {
      margin-bottom: 20px;

      .detail-row {
        margin-bottom: 8px;

        .detail-label {
          color: rgba(255, 255, 255, 0.7);
          font-size: 16px;
          font-weight: 400;
          display: block;
        }

        .detail-value {
          color: #ffffff;
          font-size: 16px;
          font-weight: 400;
          display: block;

          &.appointment-type {
            font-weight: 400;
            margin-top: -2px;
          }
        }
      }
    }

    // Consultation Fee
    .consultation-fee-section {
      margin-bottom: 16px;

      .fee-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 4px;
      }

      .fee-amount {
        color: #ffffff;
        font-size: 16px;
        font-weight: 400;
        margin-top: 0px;
      }
    }

    // Total Section
    .total-section {
      margin-bottom: 32px;
      padding-top: 16px;
      padding-bottom: 16px;
      border-top: 1px solid #ffffff;
      border-bottom: 1px solid #ffffff;

      .total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .total-label {
          color: #ffffff;
          font-size: 16px;
          font-weight: 500;
        }

        .total-amount {
          color: #ffffff;
          font-size: 18px;
          font-weight: 600;
        }
      }
    }

    // Payment Mode Section
    .payment-mode-section {
      margin-bottom: 32px;

      .payment-mode-label {
        color: #ffffff;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 12px;
      }

      ion-radio-group {
        .payment-option {
          margin-bottom: 0px;

          .payment-item {
            --background: transparent;
            --color: #ffffff;
            --border-color: transparent;
            --inner-border-width: 0;
            --padding-start: 0;
            --padding-end: 0;
            --padding-top: 4px;
            --padding-bottom: 4px;

            ion-radio {
              --color: rgba(255, 255, 255, 0.5);
              --color-checked: #ffffff;
              margin-right: 16px;
            }

            .payment-option-content {
              display: flex;
              align-items: center;
              gap: 12px;

              .payment-icon {
                width: 26px;
                height: 26px;
                display: block;
                object-fit: contain;
              }

              .payment-label {
                color: #ffffff;
                font-size: 16px;
                font-weight: 400;
                margin: 0;
              }
            }
          }
        }
      }
    }
  }
  
  // Next Button (matching book appointment button styling)
  .next-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: transparent;

    .next-button {
      --background: #004AD9;
      --background-activated: #0056CC;
      --background-hover: #0056CC;
      --color: #ffffff;
      --border-radius: 8px;
      --padding-top: 12px;
      --padding-bottom: 12px;
      --text-transform: none;

      font-size: 18px;
      font-weight: 400;
      height: 48px;
      margin: 0;
      margin-bottom: 20px;
      text-transform: none;
    }
  }
}
