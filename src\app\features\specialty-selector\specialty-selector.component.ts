import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonContent,
  IonButton,
  IonItem,
  IonLabel,
  ModalController
} from '@ionic/angular/standalone';

@Component({
  selector: 'app-specialty-selector',
  templateUrl: './specialty-selector.component.html',
  styleUrls: ['./specialty-selector.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonContent,
    IonButton,
    IonItem,
    IonLabel
  ]
})
export class SpecialtySelectorComponent {
  @Input() selectedSpecialty: string = '';

  specialties = [
    'General Physician',
    'Cardiologist',
    'Dermatologist',
    'Pediatrician',
    'Orthopedic',
    'Neurologist'
  ];

  constructor(private modalController: ModalController) {}

  selectSpecialty(specialty: string) {
    this.modalController.dismiss({
      selectedSpecialty: specialty
    });
  }

  close() {
    this.modalController.dismiss();
  }
}
