// Waiting Page Styles
.waiting-content {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;
  background: linear-gradient(180deg, #023753 0%, #020621 100%);
  
  .waiting-container {
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 100vh;
    background: linear-gradient(180deg, #023753 0%, #020621 100%);
    
    // Animation
    .animation-container {
      margin-top: 180px;
      margin-bottom: 40px;

      .loading-animation {
        width: 80px;
        height: 80px;
        display: block;
      }
    }
    
    // Main Title
    .main-title {
      color: #ffffff;
      font-size: 24px;
      font-weight: 500;
      margin-bottom: 20px;
      line-height: 1.2;
    }

    // Subtitle
    .subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 16px;
      font-weight: 300;
      line-height: 1.3;
      margin-bottom: 40px;
      max-width: 340px;
      padding: 0 24px;
    }
    
    // Tips List
    .tips-list {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 24px;
      padding: 0 24px;
      max-width: 330px;

      .tip-item {
        display: flex;
        align-items: flex-start;
        text-align: left;
        gap: 16px;

        .tip-icon {
          flex-shrink: 0;
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.15);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 2px;

          ion-icon {
            color: #ffffff;
            font-size: 20px;
          }
        }

        .tip-text {
          color: #ffffff;
          font-size: 16px;
          font-weight: 400;
          line-height: 1.4;
          flex: 1;
        }
      }
    }
  }
}
