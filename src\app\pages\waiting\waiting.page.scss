// Waiting Page Styles
.waiting-content {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;
  background: linear-gradient(180deg, #023753 0%, #020621 100%);
  
  .waiting-container {
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 100vh;
    background: linear-gradient(180deg, #023753 0%, #020621 100%);
    
    // Status Bar
    .status-bar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px 8px 20px;
      margin-bottom: 60px;
      
      .time {
        color: #ffffff;
        font-size: 17px;
        font-weight: 600;
      }
      
      .status-icons {
        display: flex;
        align-items: center;
        gap: 6px;
        
        .signal-bars {
          display: flex;
          align-items: flex-end;
          gap: 2px;
          
          .bar {
            width: 3px;
            background: #ffffff;
            border-radius: 1px;
            
            &:nth-child(1) { height: 4px; }
            &:nth-child(2) { height: 6px; }
            &:nth-child(3) { height: 8px; }
            &:nth-child(4) { height: 10px; }
          }
        }
        
        .wifi-icon, .battery-icon {
          color: #ffffff;
          font-size: 16px;
        }
      }
    }
    
    // Star Icon
    .star-icon-container {
      margin-bottom: 40px;
      
      .star-icon {
        color: #ffffff;
        font-size: 80px;
        font-weight: 300;
        line-height: 1;
      }
    }
    
    // Main Title
    .main-title {
      color: #ffffff;
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 16px;
    }
    
    // Subtitle
    .subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      font-weight: 400;
      line-height: 1.4;
      margin-bottom: 48px;
      max-width: 280px;
      padding: 0 20px;
    }
    
    // Tips List
    .tips-list {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 32px;
      padding: 0 32px;
      
      .tip-item {
        display: flex;
        align-items: flex-start;
        text-align: left;
        gap: 20px;
        
        .tip-icon {
          flex-shrink: 0;
          width: 44px;
          height: 44px;
          background: rgba(255, 255, 255, 0.15);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 2px;
          
          ion-icon {
            color: #ffffff;
            font-size: 22px;
          }
        }
        
        .tip-text {
          color: #ffffff;
          font-size: 17px;
          font-weight: 400;
          line-height: 1.4;
          flex: 1;
          margin-top: 10px;
        }
      }
    }
  }
}
