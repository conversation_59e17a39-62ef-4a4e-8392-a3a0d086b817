// Waiting Page Styles
.waiting-content {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;
  background: linear-gradient(180deg, #023753 0%, #020621 100%);
  
  .waiting-container {
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 100vh;
    background: linear-gradient(180deg, #023753 0%, #020621 100%);
    
    // Animation
    .animation-container {
      margin-top: 60px;
      margin-bottom: 32px;

      .loading-animation {
        width: 60px;
        height: 60px;
        display: block;
      }
    }

    // Main Title
    .main-title {
      color: #ffffff;
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 16px;
      line-height: 1.2;
    }

    // Subtitle
    .subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 15px;
      font-weight: 400;
      line-height: 1.3;
      margin-bottom: 32px;
      max-width: 280px;
      padding: 0 24px;
    }
    
    // Tips List
    .tips-list {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding: 0 32px;
      max-width: 320px;

      .tip-item {
        display: flex;
        align-items: flex-start;
        text-align: left;
        gap: 16px;

        .tip-icon {
          flex-shrink: 0;
          width: 36px;
          height: 36px;
          background: rgba(255, 255, 255, 0.15);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 2px;

          img {
            width: 18px;
            height: 18px;
            filter: brightness(0) invert(1); // Makes SVG white
          }
        }

        .tip-text {
          color: #ffffff;
          font-size: 15px;
          font-weight: 400;
          line-height: 1.3;
          flex: 1;
          margin-top: 6px;
        }
      }
    }
  }
}
