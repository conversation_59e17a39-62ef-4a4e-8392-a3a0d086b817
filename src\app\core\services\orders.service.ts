import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { URLS } from '../constants/index';

@Injectable({
  providedIn: 'root',
})
export class OrderService {
  private filterDataSubject = new BehaviorSubject<any>(null);
  private currentTabSubject = new BehaviorSubject<string | null>('1');
  public shareOrderDetails = new BehaviorSubject<any>(null);
  currentOrderDetails = this.shareOrderDetails.asObservable();

  constructor(private httpClient: HttpClient) {}

  private orderId: string | null = null;

  setOrderId(orderId: string): void {
    this.orderId = orderId;
  }

  getOrderId(): string | null {
    return this.orderId;
  }

  orderListing(payload: any, key: any, prescriptionId?: any) {
    let params;
    if (key) {
      params = new HttpParams();
      params = params.append('key', key);
    }
    if (prescriptionId) {
      params = new HttpParams();
      params = params.append('prescriptionId', prescriptionId);
    }
    return this.httpClient.post<any>(URLS.ORDER_LISTING, payload, { params });
  }
  orderDetails(id: any) {
    let params = new HttpParams();
    params = params.append('orderId', id);
    return this.httpClient.post<any>(URLS.ORDER_DETAILS, {}, { params });
  }
  orderMedicationDetails(id: any) {
    return this.httpClient.get<any>(
      URLS.urlWithParams(URLS.ORDER_MEDICATION_DETAILS, [
        { key: 'orderId', value: id },
      ])
    );
  }
  orderFilters():Observable<any> {
    return this.httpClient.get<any>(URLS.ORDER_FILTERS);
  }

  submitRateReview(payload: any):Observable<any> {
    return this.httpClient.post<any>(URLS.SUBMIT_RATEREVIEW, payload, {});
  }

  filterData$ = this.filterDataSubject.asObservable();

  setFilterData(filterData: any) {
    this.filterDataSubject.next(filterData);
  }

  clearFilterData() {
    this.filterDataSubject.next(null);
  }

  currentTab$ = this.currentTabSubject.asObservable();

  setCurrentTab(tab: string): void {
    this.currentTabSubject.next(tab);
  }

  modifyOrderDetails(payload: any):Observable<any> {
    return this.httpClient.post<any>(URLS.MODIFY_ORDER_DETAILS, payload);
  }
  getCancellationReasonList():Observable<any> {
    return this.httpClient.get<any>(URLS.CANCELLATION_REASONS);
  }
  orderCancel(Details: any): Observable<any> {
    return this.httpClient.post<any>(URLS.ORDER_CANCEL, Details);
  }

  placeOrder(details: any):Observable<any> {
    // let params = new HttpParams();
    // params = params.append('orderId', id);
    return this.httpClient.post<any>(URLS.PAYMENT_ON_DELIVERY, details);
  }
  rescheduleOrder(details: any):Observable<any> {
    return this.httpClient.post<any>(URLS.ORDER_RESCHEDULE, details);
  }
  sendOrderDetails(data: any) {
    this.shareOrderDetails.next(data);
  }

  getDateTime(details: any):Observable<any>{
    return this.httpClient.post<any>(URLS.DATE_TIME, details);
  }

  snehaPrescriptionDetails(prescriptionId: any, payload: any):Observable<any> {
    let params = new HttpParams();
    if (prescriptionId) {
      params = params.append('prescriptionId', prescriptionId);
    }
    return this.httpClient.post<any>(URLS.SNEHA_PRESCRIPTION, payload, {
      params,
    });
  }

  placeOrderDetails(payload: any):Observable<any> {
    return this.httpClient.post<any>(URLS.PLACE_ORDER, payload);
  }
  getLeaflet(apiUrl: string): Observable<any> {
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${localStorage.getItem('accessToken')}`
    );
    return this.httpClient.get(apiUrl, { headers });
  }
}
