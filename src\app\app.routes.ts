import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/online-doctor',
    pathMatch: 'full'
  },
  {
    path: 'online-doctor',
    loadComponent: () => import('./pages/online-doctor/online-doctor.page').then(m => m.OnlineDoctorPage)
  },
  {
    path: 'book-appointment',
    loadComponent: () => import('./pages/book-appointment/book-appointment.page').then(m => m.BookAppointmentPage)
  },
  {
    path: 'doctor/:id',
    loadComponent: () => import('./pages/doctor/doctor.page').then(m => m.DoctorPage)
  },
  {
    path: 'appointment-confirm',
    loadComponent: () => import('./pages/appointment-confirm/appointment-confirm.page').then(m => m.AppointmentConfirmPage)
  },
  {
    path: 'payment-confirmation',
    loadComponent: () => import('./pages/payment-confirmation/payment-confirmation.page').then(m => m.PaymentConfirmationPage)
  },
  {
    path: 'waiting',
    loadComponent: () => import('./pages/waiting/waiting.page').then(m => m.WaitingPage)
  },
  {
    path: 'splash-screen',
    loadComponent: () => import('./features/splash-screen/splash-screen.component').then(m => m.SplashScreenComponent)
  },
  {
    path: '**',
    redirectTo: '/online-doctor'
  }
];
