<ion-content class="facility-selector-content">
  <div class="facility-selector-container">
    <!-- Header -->
    <div class="selector-header">
      <h3>Select Facility</h3>
    </div>
    
    <!-- Facility Options -->
    <div class="facility-options">
      <div 
        *ngFor="let facility of facilities" 
        class="facility-option"
        (click)="selectFacility(facility)">
        <span>{{ facility }}</span>
      </div>
      
      <!-- Coming Soon Text -->
      <div class="coming-soon">
        other facilities coming soon!
      </div>
    </div>
    
    <!-- Close Button -->
    <div class="close-button-container">
      <ion-button 
        expand="block" 
        class="close-button"
        (click)="close()">
        Close
      </ion-button>
    </div>
  </div>
</ion-content>
