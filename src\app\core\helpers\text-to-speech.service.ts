import { Injectable } from '@angular/core';
import { TextToSpeech } from '@capacitor-community/text-to-speech';

@Injectable({
  providedIn: 'root',
})
export class TextToSpeechService {
  private isPlaying = false;

  constructor() {}

  async speak(text: string, lang: string) {
    if (this.isPlaying) {
      await this.pause();
    }
    this.isPlaying = true;
    await TextToSpeech.speak({
      text,
      lang: lang,
      rate: 1.0,
    });
  }

  async pause() {
    console.log('pause', this.isPlaying);
    if (this.isPlaying) {
      await TextToSpeech.stop();
      this.isPlaying = false;
    }
  }
}
