{"name": "vcp-webview-sdk", "version": "0.0.1", "author": "<PERSON><PERSON>", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:uat": "ng build --configuration uat", "build:dev": "ng build --configuration dev", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint --fix", "prepare": "cd ../.. && husky frontend/vcp_webview_sdk/.husky"}, "private": true, "dependencies": {"@angular-magic/ngx-gp-autocomplete": "^2.0.2", "@angular/animations": "^18.0.0", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@azure/communication-calling": "^1.37.1", "@azure/communication-common": "^2.4.0", "@capacitor/app": "6.0.0", "@capacitor/browser": "^6.0.3", "@capacitor/camera": "^6.0.1", "@capacitor/core": "6.1.1", "@capacitor/haptics": "6.0.0", "@capacitor/keyboard": "^6.0.1", "@capacitor/status-bar": "6.0.0", "@capawesome/capacitor-file-picker": "^6.0.0", "@ionic/angular": "^8.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "ionicons": "^7.2.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "typings": "./src/typings.d.ts", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.0", "@angular-eslint/builder": "^18.0.0", "@angular-eslint/eslint-plugin": "^18.0.0", "@angular-eslint/eslint-plugin-template": "^18.0.0", "@angular-eslint/schematics": "^18.0.0", "@angular-eslint/template-parser": "^18.0.0", "@angular/cli": "^18.0.0", "@angular/compiler-cli": "^18.0.0", "@angular/language-service": "^18.0.0", "@capacitor/cli": "6.1.1", "@ionic/angular-toolkit": "^11.0.1", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "husky": "^9.1.6", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.3.3", "pretty-quick": "^4.0.0", "typescript": "~5.4.0"}, "description": "An Ionic project"}