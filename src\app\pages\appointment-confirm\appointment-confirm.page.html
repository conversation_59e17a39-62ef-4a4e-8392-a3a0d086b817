<ion-header class="confirmation-header">
  <ion-toolbar class="confirmation-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()" class="back-button">
        <ion-icon name="chevron-back" color="light"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="confirmation-title">Confirm Appointment</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="confirmation-content">
  <div class="confirmation-container">
    
    <!-- Appointment Date & Time -->
    <div class="appointment-datetime">
      <h2 class="datetime-text">{{ formattedDate }}</h2>
    </div>

    <!-- Doctor Info Section -->
    <div class="doctor-info-section">
      <div class="specialty-label">{{ doctor.specialty }}</div>
      <div class="doctor-name-container">
        <h3 class="doctor-name">{{ doctor.name }}</h3>
        <div class="doctor-avatar">
          <img src="assets/images/doctor-image.jpg" [alt]="doctor.name">
        </div>
      </div>
    </div>

    <!-- Appointment Type -->
    <div class="appointment-details">
      <div class="detail-row">
        <span class="detail-label">Type of Appointment:</span>
      </div>
      <div class="detail-row">
        <span class="detail-value appointment-type">{{ appointmentType | titlecase }}</span>
      </div>
    </div>

    <!-- Payment Option -->
    <div class="payment-section">
      <div class="payment-label">Payment option</div>
      <div class="payment-toggle-container">
        <div class="payment-toggle-buttons">
          <button
            class="toggle-button"
            [class.active]="isInsuranceSelected"
            (click)="selectPaymentMethod('insurance')">
            Insurance
          </button>
          <button
            class="toggle-button"
            [class.active]="!isInsuranceSelected"
            (click)="selectPaymentMethod('self-pay')">
            Self-pay
          </button>
        </div>
      </div>
    </div>

    <!-- Consultation Fee -->
    <div class="consultation-fee-section">
      <div class="fee-label">Consultation Fee:</div>
    </div>
    <div class="consultation-fee-section">
      <div class="fee-amount">{{ consultationFee }}</div>
    </div>

    <!-- Important Notice -->
    <div class="notice-section">
      <div class="notice-icon">
        <img src="assets/images/alert.svg" alt="Alert" class="alert-icon">
      </div>
      <div class="notice-text">
        Consultation fee will be charged before the teleconsultation
      </div>
    </div>

  </div>

  <!-- Confirm Button -->
  <div class="confirm-button-container">
    <ion-button 
      expand="block" 
      class="confirm-button"
      (click)="confirmAppointment()">
      Confirm Appointment
    </ion-button>
  </div>
</ion-content>
