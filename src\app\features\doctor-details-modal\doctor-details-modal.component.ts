import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonItem,
  IonLabel,
  ModalController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { chevronBack, star, language, calendar, location } from 'ionicons/icons';
import { PaymentOptionModalComponent } from '../payment-option-modal/payment-option-modal.component';
import { AppointmentConfirmationComponent } from '../appointment-confirmation/appointment-confirmation.component';

export interface Doctor {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  ratingCount: number;
  languages: string;
  earliestTime: string;
  avatar: string;
  availability?: {
    [date: string]: {
      afternoon: string[];
      evening: string[];
    };
  };
}

@Component({
  selector: 'app-doctor-details-modal',
  templateUrl: './doctor-details-modal.component.html',
  styleUrls: ['./doctor-details-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonItem,
    IonLabel
  ]
})
export class DoctorDetailsModalComponent implements OnInit {
  @Input() doctor!: Doctor;
  @Input() initialSelectedDate: string = '30/1';

  selectedTab: string = 'in-person';
  selectedDate: string = '30/1';
  selectedTime: string = '12:30 PM';

  availableDates = [
    { day: 'Fri', date: '30/1', value: '30/1' },
    { day: 'Sat', date: '31/1', value: '31/1' },
    { day: 'Sun', date: '1/2', value: '1/2' },
    { day: 'Mon', date: '2/2', value: '2/2' },
    { day: 'Tue', date: '3/2', value: '3/2' },
    { day: 'Wed', date: '4/2', value: '4/2' },
    { day: 'Thu', date: '5/2', value: '5/2' },
    { day: 'Fri', date: '6/2', value: '6/2' },
    { day: 'Sat', date: '7/2', value: '7/2' },
    { day: 'Sun', date: '8/2', value: '8/2' },
    { day: 'Mon', date: '9/2', value: '9/2' },
    { day: 'Tue', date: '10/2', value: '10/2' },
    { day: 'Wed', date: '11/2', value: '11/2' },
    { day: 'Thu', date: '12/2', value: '12/2' },
    { day: 'Fri', date: '13/2', value: '13/2' },
    { day: 'Sat', date: '14/2', value: '14/2' },
    { day: 'Sun', date: '15/2', value: '15/2' },
    { day: 'Mon', date: '16/2', value: '16/2' },
    { day: 'Tue', date: '17/2', value: '17/2' },
    { day: 'Wed', date: '18/2', value: '18/2' }
  ];

  afternoonSlots: { time: string; disabled: boolean }[] = [];
  eveningSlots: { time: string; disabled: boolean }[] = [];

  constructor(private modalController: ModalController) {
    addIcons({
      chevronBack,
      star,
      language,
      calendar,
      location
    });
  }

  ngOnInit() {
    // Set the selected date from the input if provided
    if (this.initialSelectedDate) {
      this.selectedDate = this.initialSelectedDate;
    }
    // Generate time slots based on doctor's availability
    this.generateTimeSlots();
  }

  selectTab(tab: string) {
    this.selectedTab = tab;
  }

  selectDate(date: string) {
    this.selectedDate = date;
    this.generateTimeSlots();
  }

  selectTime(time: string) {
    this.selectedTime = time;
  }

  generateTimeSlots() {
    // Reset slots
    this.afternoonSlots = [];
    this.eveningSlots = [];

    if (this.doctor.availability && this.doctor.availability[this.selectedDate]) {
      const dayAvailability = this.doctor.availability[this.selectedDate];

      // Combine all available times and sort them
      const allTimes = [...dayAvailability.afternoon, ...dayAvailability.evening];
      const sortedTimes = allTimes.sort((a, b) => {
        return this.convertTimeToMinutes(a) - this.convertTimeToMinutes(b);
      });

      // Categorize times into afternoon (before 6 PM) and evening (6 PM and after)
      sortedTimes.forEach(time => {
        const timeInMinutes = this.convertTimeToMinutes(time);
        const sixPM = 18 * 60; // 6 PM in minutes

        if (timeInMinutes < sixPM) {
          this.afternoonSlots.push({ time, disabled: false });
        } else {
          this.eveningSlots.push({ time, disabled: false });
        }
      });

      // Set the first available time as selected (earliest time)
      if (sortedTimes.length > 0) {
        this.selectedTime = sortedTimes[0];
      }
    } else {
      // Default: only show available slots
      this.afternoonSlots = [
        { time: '12:30 PM', disabled: false }
      ];
      this.eveningSlots = [
        { time: '7:00 PM', disabled: false }
      ];
      this.selectedTime = '12:30 PM';
    }
  }

  convertTimeToMinutes(time: string): number {
    const [timePart, period] = time.split(' ');
    const [hours, minutes] = timePart.split(':').map(Number);
    let totalMinutes = hours * 60 + (minutes || 0);

    if (period === 'PM' && hours !== 12) {
      totalMinutes += 12 * 60;
    } else if (period === 'AM' && hours === 12) {
      totalMinutes -= 12 * 60;
    }

    return totalMinutes;
  }

  dismiss() {
    this.modalController.dismiss();
  }

  async bookAppointment() {
    // Show payment option modal as bottom sheet
    const paymentModal = await this.modalController.create({
      component: PaymentOptionModalComponent,
      cssClass: 'payment-option-modal',
      backdropDismiss: true,
      breakpoints: [0, 0.4, 0.6],
      initialBreakpoint: 0.4,
      handle: false
    });

    paymentModal.onDidDismiss().then(async (result) => {
      if (result.data && result.data.paymentMethod) {
        // Close the doctor details modal first
        await this.modalController.dismiss();

        // Open appointment confirmation modal
        const confirmationModal = await this.modalController.create({
          component: AppointmentConfirmationComponent,
          componentProps: {
            doctor: this.doctor,
            appointmentType: this.selectedTab,
            selectedDate: this.selectedDate,
            selectedTime: this.selectedTime,
            paymentMethod: result.data.paymentMethod
          },
          cssClass: 'appointment-confirmation-modal'
        });

        confirmationModal.onDidDismiss().then((confirmResult) => {
          if (confirmResult.data && confirmResult.data.confirmed) {
            // Handle successful appointment confirmation
            console.log('Appointment confirmed:', confirmResult.data.appointmentData);
          }
        });

        await confirmationModal.present();
      }
    });

    await paymentModal.present();
  }
}
