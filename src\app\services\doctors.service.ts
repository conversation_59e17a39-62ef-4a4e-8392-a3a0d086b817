import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Doctor {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  ratingCount: number;
  languages: string;
  earliestTime: string;
  avatar: string;
  availability?: {
    [date: string]: {
      afternoon: string[];
      evening: string[];
    };
  };
}

interface DoctorsResponse {
  doctors: Doctor[];
}

@Injectable({
  providedIn: 'root'
})
export class DoctorsService {

  constructor(private http: HttpClient) { }

  getDoctors(): Observable<Doctor[]> {
    return this.http.get<DoctorsResponse>('assets/mock-data/doctors.json')
      .pipe(
        map(response => response.doctors)
      );
  }

  getDoctorsBySpecialty(specialty: string): Observable<Doctor[]> {
    return this.getDoctors().pipe(
      map(doctors => doctors.filter(doctor => 
        specialty === '' || doctor.specialty === specialty
      ))
    );
  }

  getDoctorById(id: number): Observable<Doctor | undefined> {
    return this.getDoctors().pipe(
      map(doctors => doctors.find(doctor => doctor.id === id))
    );
  }
}
