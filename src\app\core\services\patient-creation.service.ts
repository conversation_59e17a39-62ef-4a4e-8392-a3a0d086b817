import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { URLS } from '../constants/index';

@Injectable({
  providedIn: 'root',
})
export class patientCreationService {
  constructor(private httpClient: HttpClient) {}

  getDropDowns():Observable<any> {
    // const headers = new HttpHeaders().set(
    //   'Authorization',
    //   `Bearer ${localStorage.getItem('accessToken')}`
    // );
    return this.httpClient.get<any>(URLS.PATIENT_CREATION);
  }

  createPatient(payload: any):Observable<any> {
    // const headers = new HttpHeaders().set(
    //   'Authorization',
    //   `Bearer ${localStorage.getItem('accessToken')}`
    // );
    return this.httpClient.post<any>(URLS.CREATE_PATIENT, payload);
  }

  patientDetails():Observable<any> {
    // const headers = new HttpHeaders().set(
    //   'Authorization',
    //   `Bearer ${localStorage.getItem('accessToken')}`
    // );
    return this.httpClient.get<any>(URLS.PATIENT_DETAILS);
  }
}
