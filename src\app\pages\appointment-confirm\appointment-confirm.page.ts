import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonToggle
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { chevronBack } from 'ionicons/icons';
import { DateFormatterService } from '../../services/date-formatter.service';

interface Doctor {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  ratingCount: number;
  languages: string;
  earliestTime: string;
  avatar: string;
}

@Component({
  selector: 'app-appointment-confirm',
  templateUrl: './appointment-confirm.page.html',
  styleUrls: ['./appointment-confirm.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonToggle
  ]
})
export class AppointmentConfirmPage implements OnInit {
  doctor!: Doctor;
  appointmentType: string = 'online';
  selectedDate: string = '';
  selectedTime: string = '';
  paymentMethod: string = '';

  // Toggle state for payment method
  isInsuranceSelected: boolean = false;

  constructor(
    private router: Router,
    private location: Location,
    private dateFormatter: DateFormatterService
  ) {
    addIcons({
      chevronBack
    });
  }

  ngOnInit() {
    // Get appointment data from navigation state
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state) {
      this.doctor = navigation.extras.state['doctor'];
      this.appointmentType = navigation.extras.state['appointmentType'] || 'online';
      this.selectedDate = navigation.extras.state['selectedDate'] || '';
      this.selectedTime = navigation.extras.state['selectedTime'] || '';
      this.paymentMethod = navigation.extras.state['paymentMethod'] || '';
    }

    // Set toggle state based on payment method
    this.isInsuranceSelected = this.paymentMethod === 'insurance';

    // If no data in navigation state, use mock data
    if (!this.doctor) {
      this.doctor = {
        id: 1,
        name: 'Dr. Abdur Raheem',
        specialty: 'General Physician',
        rating: 4.9,
        ratingCount: 40,
        languages: 'English, Arabic',
        earliestTime: '12:30 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR'
      };
      this.selectedDate = '30/1';
      this.selectedTime = '12:30 PM';
      this.appointmentType = 'online';
      this.paymentMethod = 'insurance';
      this.isInsuranceSelected = true;
    }
  }

  get formattedDate(): string {
    return this.dateFormatter.formatDateTime(this.selectedDate, this.selectedTime);
  }

  get consultationFee(): string {
    // Mock consultation fee based on appointment type
    return this.appointmentType === 'online' ? 'AED 50' : 'AED 99';
  }

  goBack() {
    // Add console log to debug
    console.log('Going back with doctor:', this.doctor);
    console.log('Doctor ID:', this.doctor?.id);
    console.log('Selected data:', {
      date: this.selectedDate,
      time: this.selectedTime,
      type: this.appointmentType,
      payment: this.paymentMethod
    });

    try {
      // Check if doctor exists and has an ID
      if (this.doctor && this.doctor.id) {
        this.router.navigate(['/doctor', this.doctor.id], {
          state: {
            doctor: this.doctor,
            selectedDate: this.selectedDate,
            selectedTime: this.selectedTime,
            appointmentType: this.appointmentType,
            paymentMethod: this.paymentMethod
          }
        }).then(success => {
          if (!success) {
            console.error('Navigation failed, trying location.back()');
            this.location.back();
          }
        }).catch(error => {
          console.error('Navigation error:', error);
          this.location.back();
        });
      } else {
        // Fallback to book-appointment page if doctor is missing
        console.warn('Doctor object is missing or invalid, navigating to book-appointment');
        this.router.navigate(['/book-appointment'], {
          state: {
            selectedDate: this.selectedDate,
            appointmentType: this.appointmentType
          }
        }).catch(error => {
          console.error('Fallback navigation error:', error);
          this.location.back();
        });
      }
    } catch (error) {
      console.error('Unexpected error in goBack:', error);
      // Last resort - use browser back
      this.location.back();
    }
  }

  selectPaymentMethod(method: string) {
    this.paymentMethod = method;
    this.isInsuranceSelected = method === 'insurance';
  }

  confirmAppointment() {
    // Navigate to payment confirmation page
    this.router.navigate(['/payment-confirmation'], {
      state: {
        doctor: this.doctor,
        appointmentType: this.appointmentType,
        selectedDate: this.selectedDate,
        selectedTime: this.selectedTime,
        paymentMethod: this.paymentMethod
      }
    });
  }
}
