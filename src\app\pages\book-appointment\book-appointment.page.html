<ion-header class="transparent-header">
  <ion-toolbar class="transparent-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()">
        <ion-icon name="chevron-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>
      Book Appointment
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="booking-content">
  <!-- Appointment Type Tabs -->
  <div class="appointment-tabs">
    <ion-segment [(ngModel)]="selectedSegment" class="custom-segment">
      <ion-segment-button value="online" class="segment-button">
        <ion-label>Online</ion-label>
      </ion-segment-button>
      <ion-segment-button value="in-person" class="segment-button">
        <ion-label>In-Person</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <!-- Online Filter Section -->
  <div class="filter-section" *ngIf="selectedSegment === 'online'">
    <div class="custom-dropdown specialty-dropdown"
         [class.selected]="selectedSpecialty !== 'Select Speciality'"
         (click)="openSpecialtySelector()">
      <span class="dropdown-text">{{ selectedSpecialty }}</span>
      <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
        <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <div class="custom-filter-button" (click)="openFilters()">
      <img src="assets/images/funnel-icon.svg" alt="Filter" class="filter-icon">
    </div>
  </div>

  <!-- In-Person Filter Section -->
  <div class="inperson-filter-section" *ngIf="selectedSegment === 'in-person'">
    <div class="custom-dropdown facility-dropdown"
         [class.selected]="selectedFacility !== 'Select Facility'"
         (click)="openFacilitySelector()">
      <span class="dropdown-text">{{ selectedFacility }}</span>
      <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
        <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <div class="custom-dropdown specialty-dropdown"
         [class.selected]="selectedSpecialty !== 'Select Speciality'"
         (click)="openSpecialtySelector()">
      <span class="dropdown-text">{{ selectedSpecialty }}</span>
      <svg class="dropdown-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
        <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <div class="custom-filter-button" (click)="openFilters()">
      <img src="assets/images/funnel-icon.svg" alt="Filter" class="filter-icon">
    </div>
  </div>
    <!-- Date Selection -->
  <div class="date-selection">
    <div class="date-scroll">
      <div *ngFor="let date of availableDates"
           class="date-item"
           [class.selected]="selectedDate === date.value"
           (click)="selectDate(date.value)">
        <div class="day">{{ date.day }}</div>
        <div class="date">{{ date.date }}</div>
      </div>
    </div>
  </div>

  <!-- In-Person Empty State Message -->
  <div *ngIf="selectedSegment === 'in-person' && (selectedFacility === 'Select Facility' || selectedSpecialty === 'Select Speciality')" class="inperson-empty-message">
    Please select facility and speciality to view results
  </div>



  <!-- Search Bar (only visible for online) -->
  <div class="search-section" *ngIf="selectedSegment === 'online'">
    <ion-searchbar
      [(ngModel)]="searchTerm"
      placeholder="Search for doctors"
      class="custom-searchbar"
      (ionInput)="onSearchChange($event)">
    </ion-searchbar>
  </div>

  <!-- Online Doctor List -->
  <div class="online-doctor-list" *ngIf="selectedSegment === 'online'">
    <div *ngFor="let doctor of filteredDoctors" class="online-doctor-card" (click)="selectDoctorOnline(doctor)">
      <div class="doctor-card-content">
        <div class="doctor-main-info">
          <div class="doctor-left-section">
            <div class="doctor-avatar">
              <img src="assets/images/doctor-image.jpg" [alt]="doctor.name">
            </div>
            <div class="doctor-details">
              <div class="doctor-name-specialty">
                <h3 class="doctor-name">{{ doctor.name }}</h3>
                <p class="doctor-specialty">{{ doctor.specialty }}</p>
              </div>
              <div class="doctor-languages">
                <ion-icon name="language"></ion-icon>
                <span>{{ doctor.languages }}</span>
              </div>
              <div class="doctor-availability">
                <span class="available-text">Earliest available at:</span>
                <div class="time-chip">{{ doctor.earliestTime }}</div>
              </div>
            </div>
          </div>
          <div class="doctor-right-section">
            <div class="doctor-rating">
              <ion-icon name="star"></ion-icon>
              <span class="rating-text">{{ doctor.rating }} ({{ doctor.ratingCount }})</span>
            </div>
            <ion-icon name="chevron-forward" class="arrow-icon"></ion-icon>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- In-Person Doctors Component -->
  <app-in-person-doctors
    *ngIf="selectedSegment === 'in-person'"
    [selectedFacility]="selectedFacility"
    [selectedSpecialty]="selectedSpecialty"
    [selectedDate]="selectedDate"
    (doctorSelected)="selectDoctor($event)">
  </app-in-person-doctors>
</ion-content>
