import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonRadioGroup,
  IonRadio,
  IonItem,
  IonLabel,
  ModalController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { chevronBack } from 'ionicons/icons';
import { Doctor } from '../../models/doctor.model';
import { DateFormatterService } from '../../services/date-formatter.service';
import { ConsultationTipsModalComponent } from '../../features/consultation-tips-modal/consultation-tips-modal.component';

@Component({
  selector: 'app-payment-confirmation',
  templateUrl: './payment-confirmation.page.html',
  styleUrls: ['./payment-confirmation.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonRadioGroup,
    IonRadio,
    IonItem,
    IonLabel
  ]
})
export class PaymentConfirmationPage implements OnInit {
  doctor!: Doctor;
  appointmentType: string = 'online';
  selectedDate: string = '';
  selectedTime: string = '';
  paymentMethod: string = '';
  consultationFee: string = '99 AED';
  selectedPaymentMode: string = 'credit-card';

  constructor(
    private router: Router,
    private dateFormatter: DateFormatterService,
    private modalController: ModalController
  ) {
    addIcons({
      chevronBack
    });
  }

  ngOnInit() {
    // Get appointment data from navigation state
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state) {
      this.doctor = navigation.extras.state['doctor'];
      this.appointmentType = navigation.extras.state['appointmentType'] || 'online';
      this.selectedDate = navigation.extras.state['selectedDate'] || '';
      this.selectedTime = navigation.extras.state['selectedTime'] || '';
      this.paymentMethod = navigation.extras.state['paymentMethod'] || '';
    }

    // If no data in navigation state, use mock data
    if (!this.doctor) {
      this.doctor = {
        id: 1,
        name: 'Dr Moneeb Bhatti',
        specialty: 'Family Medicine General',
        rating: 4.9,
        ratingCount: 40,
        languages: 'English, Arabic',
        earliestTime: '6:30 PM',
        avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR'
      };
      this.selectedDate = '24/11';
      this.selectedTime = '6:30 PM';
      this.appointmentType = 'online';
    }
  }

  get formattedDateTime(): string {
    return this.dateFormatter.formatDateTime(this.selectedDate, this.selectedTime);
  }

  goBack() {
    this.router.navigate(['/appointment-confirm'], {
      state: {
        doctor: this.doctor,
        appointmentType: this.appointmentType,
        selectedDate: this.selectedDate,
        selectedTime: this.selectedTime,
        paymentMethod: this.paymentMethod
      }
    });
  }

  onPaymentModeChange(event: any) {
    this.selectedPaymentMode = event.detail.value;
  }

  async proceedToNext() {
    // Handle payment processing
    console.log('Processing payment:', {
      doctor: this.doctor,
      appointmentType: this.appointmentType,
      date: this.selectedDate,
      time: this.selectedTime,
      paymentMethod: this.paymentMethod,
      paymentMode: this.selectedPaymentMode,
      amount: this.consultationFee
    });

    // Show consultation tips modal after payment processing
    const tipsModal = await this.modalController.create({
      component: ConsultationTipsModalComponent,
      cssClass: 'consultation-tips-modal',
      backdropDismiss: true,
      breakpoints: [0, 0.6, 0.8],
      initialBreakpoint: 0.6,
      handle: false
    });

    tipsModal.onDidDismiss().then(() => {
      // Navigate back to main page after tips modal is dismissed
      this.router.navigate(['/book-appointment']);
    });

    await tipsModal.present();
  }
}
