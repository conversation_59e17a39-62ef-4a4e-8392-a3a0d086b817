// For information on how to create your own theme, please see:
// http://ionicframework.com/docs/theming/

// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/theming/

/** Ionic CSS Variables **/
:root {
  /** primary **/
  --ion-color-primary: #1a2332;
  --ion-color-primary-rgb: 26, 35, 50;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #17202c;
  --ion-color-primary-tint: #313a47;

  /** secondary **/
  --ion-color-secondary: #2a3441;
  --ion-color-secondary-rgb: 42, 52, 65;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #252e39;
  --ion-color-secondary-tint: #3f4854;

  /** tertiary **/
  --ion-color-tertiary: #3a4a5c;
  --ion-color-tertiary-rgb: 58, 74, 92;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #334151;
  --ion-color-tertiary-tint: #4e5b6c;

  /** success **/
  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45, 211, 111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  /** warning **/
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  /** danger **/
  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb: 235, 68, 90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;

  /** dark **/
  --ion-color-dark: #1a2332;
  --ion-color-dark-rgb: 26, 35, 50;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #17202c;
  --ion-color-dark-tint: #313a47;

  /** medium **/
  --ion-color-medium: #8a9ba8;
  --ion-color-medium-rgb: 138, 155, 168;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #798894;
  --ion-color-medium-tint: #96a5b1;

  /** light **/
  --ion-color-light: #ffffff;
  --ion-color-light-rgb: 255, 255, 255;
  --ion-color-light-contrast: #1a2332;
  --ion-color-light-contrast-rgb: 26, 35, 50;
  --ion-color-light-shade: #e0e0e0;
  --ion-color-light-tint: #ffffff;

  // Global background
  --ion-background-color: #1a2332;
  --ion-text-color: #ffffff;

  // Toolbar
  --ion-toolbar-background: #1a2332;
  --ion-toolbar-color: #ffffff;

  // Tab bar
  --ion-tab-bar-background: #1a2332;
  --ion-tab-bar-color: #8a9ba8;
  --ion-tab-bar-color-selected: #ffffff;
}
