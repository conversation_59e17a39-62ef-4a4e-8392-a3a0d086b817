import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { URLS } from '../constants/index';
import { AddEditAddressPayload } from '../models/address.model';

@Injectable({
  providedIn: 'root',
})
export class AddressService {
  constructor(private httpClient: HttpClient) {}

  getAddresses():Observable<any> {
    return this.httpClient.get<any>(URLS.GET_ADDRESSES);
  }
  addAddress(Details: AddEditAddressPayload): Observable<any> {
    return this.httpClient.post<any>(URLS.ADD_ADDRESS, Details);
  }
  editAddress(DetailsandId: any): Observable<any> {
    return this.httpClient.post<any>(URLS.EDIT_ADDRESS, DetailsandId);
  }
  setPrimaryAddress(id: any): Observable<any> {
    return this.httpClient.post<any>(URLS.SET_PRIMARY_ADDRESS, id);
  }
  getLiveTracking(eid:any):Observable<any> {
    return this.httpClient.post<any>(URLS.LIVE_TRACKING,eid);
  }
}
