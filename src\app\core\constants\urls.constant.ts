import { environment } from 'src/environments/environment';

const API_URL = '' //environment.API_URL;
const API_URL_AUTH = ''//environment.AUTH_API_URL;
const API_URL_FOR_PATIENT = ''//environment.API_URL_FOR_PATIENT;

/* RefreshToken End Points */
export const REFRESH_TOKEN: any = `${API_URL_AUTH}/auth/sdk/refresh-token`;

/* Mobile Login End Points */
export const LOGIN: any = `${API_URL_AUTH}/auth/mobile-login`;
export const GEN_TOKEN_PERSONA: any = `${API_URL_AUTH}/auth/sdk/generate-token`;

/* Auth End Points */
export const AUTH: any = `${API_URL_AUTH}/auth/sdk/token`;

/* Address End Points */
export const GET_ADDRESSES = `${API_URL_FOR_PATIENT}/sdk/get-address`;
export const ADD_ADDRESS = `${API_URL_FOR_PATIENT}/sdk/add-address`;
export const EDIT_ADDRESS = `${API_URL_FOR_PATIENT}/sdk/edit-address`;
export const SET_PRIMARY_ADDRESS = `${API_URL_FOR_PATIENT}/sdk/set-primary-address`;
export const GET_DASHBOARD_DETAILS: any = `${API_URL_FOR_PATIENT}/sdk/dashboard`;

/* Patient List End Point */
export const GET_PATIENT_LIST = `${API_URL_FOR_PATIENT}/sdk/get-patient-list`;

/* Upload File End Points */
export const UPLOAD_FILE = `${API_URL_FOR_PATIENT}/sdk/upload-file`;

/* Medical Facility End Points */
export const MEDICAL_FACILITY = `${API_URL_FOR_PATIENT}/sdk/medical-facility`;

/* Insurance Vendors End Points */
export const INSURANCER_TYPES = `${API_URL_FOR_PATIENT}/sdk/insurance-types`;

/* Patient List End Points */
export const PATIENT_LIST = `${API_URL_FOR_PATIENT}/sdk/get-patient-list`;

/* Upload Pres End Points */
export const UPLOAD_PRESCRIPTION = `${API_URL_FOR_PATIENT}/sdk/upload`;
export const GET_UPLOADED_PRESCRIPTION = `${API_URL_FOR_PATIENT}/sdk/get/:id`;
export const EDIT_UPLOADED_PRESCRIPTION = `${API_URL_FOR_PATIENT}/sdk/edit`;
export const DUBAI_UPLOAD_PRESCRIPTION = `${API_URL_FOR_PATIENT}/sdk/upload-dp`;

/* Orders End Points */
export const ORDER_LISTING = `${API_URL_FOR_PATIENT}/sdk/get-orders-list`;
export const ORDER_DETAILS = `${API_URL_FOR_PATIENT}/sdk/get-orders`;
export const MODIFY_ORDER_DETAILS = `${API_URL_FOR_PATIENT}/sdk/update-medications`;
export const ORDER_FILTERS = `${API_URL_FOR_PATIENT}/sdk/get-filters-data`;
export const ORDER_CANCEL = `${API_URL_FOR_PATIENT}/sdk/patient-order-cancel`;
export const SUBMIT_RATEREVIEW = `${API_URL_FOR_PATIENT}/sdk/add-review`;
export const ORDER_RESCHEDULE = `${API_URL_FOR_PATIENT}/sdk/reschedule-on-delivery-failed`;
export const DATE_TIME: any = `${API_URL_FOR_PATIENT}/sdk/get-warehouse-date-time`;
export const SNEHA_PRESCRIPTION: any = `${API_URL_FOR_PATIENT}/sdk/get-prescriptions`;
export const PLACE_ORDER: any = `${API_URL_FOR_PATIENT}/sdk/place-order`;
export const ORDER_MEDICATION_DETAILS: any = `${API_URL_FOR_PATIENT}/sdk/get-medicines-detail/:orderId`;

/* Config End Point */
export const CONFIG = `${API_URL_FOR_PATIENT}/sdk/config`;

/* Payment Options */
export const PAYMENT_ON_DELIVERY = `${API_URL_FOR_PATIENT}/sdk/delivery-payment-confirmation`;

/* Tracking order End Points */
export const TRACK_ORDER = `${API_URL_FOR_PATIENT}/sdk/track-order`;

/* Cancellation Reasons End Points */
export const CANCELLATION_REASONS = `${API_URL_FOR_PATIENT}/sdk/get-cancel-reason-list`;

/* Notification End Points */
export const GET_NOTIFICATIONS = `${API_URL_FOR_PATIENT}/sdk/user-notification`;

/* Payment Verification End Points */
export const PAYMENT_VERIFICATION = `${API_URL_FOR_PATIENT}/sdk/verify-payment`;

/* Menu End Points */
export const MENU_ITEMS = `${API_URL_FOR_PATIENT}/sdk/get-menu-list`;

/* Patient Creation End Points */
export const PATIENT_CREATION = `${API_URL_FOR_PATIENT}/sdk/lookups`;
export const CREATE_PATIENT = `${API_URL_FOR_PATIENT}/sdk/create-patient`;
export const PATIENT_DETAILS = `${API_URL_FOR_PATIENT}/sdk/dashboard`;

/* Marketing Banner End Points */
export const MARKETING_BANNER = `${API_URL_FOR_PATIENT}/sdk/get-marketing-banners`;

/* live tracking End Points */
export const LIVE_TRACKING = `${API_URL_FOR_PATIENT}/sdk/live-tracking`;

/* PURA PDF End Points */
export const GET_PURA_PRESCRIPTION_PDF = `${API_URL_FOR_PATIENT}/sdk/download-prescription`;


/* URL with PARAMS */
export const urlWithParams = (
  url: string,
  params: { key: string; value: string | number }[]
) => {
  params.forEach((param: { key: string; value: string | number }) => {
    url = url.replace(':' + param.key, param.value + '');
  });

  return url;
};
