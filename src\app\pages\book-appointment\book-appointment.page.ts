import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonSearchbar,
  IonCard,
  IonCardContent,
  IonChip,
  ModalController
} from '@ionic/angular/standalone';
import { FiltersModalComponent } from '../../features/filters-modal/filters-modal.component';
import { FacilitySelectorComponent } from '../../features/facility-selector/facility-selector.component';
import { SpecialtySelectorComponent } from '../../features/specialty-selector/specialty-selector.component';
import { InPersonDoctorsComponent } from '../../features/in-person-doctors/in-person-doctors.component';
import { addIcons } from 'ionicons';
import {
  chevronBack,
  chevronDown,
  filter,
  star,
  language,
  chevronForward,
  calendar
} from 'ionicons/icons';

interface Doctor {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  ratingCount: number;
  languages: string;
  earliestTime: string;
  avatar: string;
}

interface DateOption {
  day: string;
  date: string;
  value: string;
}

@Component({
  selector: 'app-book-appointment',
  templateUrl: './book-appointment.page.html',
  styleUrls: ['./book-appointment.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonSegment,
    IonSegmentButton,
    IonLabel,
    IonSearchbar,
    IonCard,
    IonCardContent,
    IonChip,
    InPersonDoctorsComponent
  ],
})
export class BookAppointmentPage implements OnInit {
  selectedSegment = 'online';
  selectedSpecialty = 'Select Speciality';
  selectedFacility = 'Select Facility';
  selectedDate = '30/1';
  searchTerm = '';

  availableDates: DateOption[] = [
    { day: 'Wed', date: '30/1', value: '30/1' },
    { day: 'Thu', date: '31/1', value: '31/1' },
    { day: 'Fri', date: '1/2', value: '1/2' },
    { day: 'Sat', date: '2/2', value: '2/2' },
    { day: 'Sun', date: '3/2', value: '3/2' },
    { day: 'Mon', date: '4/2', value: '4/2' },
    { day: 'Tue', date: '5/2', value: '5/2' }
  ];

  doctors: Doctor[] = [
    {
      id: 1,
      name: 'Dr. Abdur Raheem',
      specialty: 'General Physician',
      rating: 4.9,
      ratingCount: 40,
      languages: 'English, Arabic',
      earliestTime: '12:30 PM',
      avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR'
    },
    {
      id: 2,
      name: 'Dr. Abdur Raheem',
      specialty: 'General Physician',
      rating: 4.9,
      ratingCount: 40,
      languages: 'English, Arabic',
      earliestTime: '12:30 PM',
      avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR'
    }
  ];

  filteredDoctors: Doctor[] = [];

  constructor(
    private modalController: ModalController,
    private router: Router
  ) {
    addIcons({
      chevronBack,
      chevronDown,
      filter,
      star,
      language,
      chevronForward,
      calendar
    });

    this.filteredDoctors = [...this.doctors];
  }

  ngOnInit() {
    // Get data from navigation state when returning from doctor page
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state) {
      if (navigation.extras.state['selectedDate']) {
        this.selectedDate = navigation.extras.state['selectedDate'];
      }
      if (navigation.extras.state['appointmentType']) {
        this.selectedSegment = navigation.extras.state['appointmentType'];
      }
    }
  }

  selectDate(date: string) {
    this.selectedDate = date;
  }

  onSearchChange(event: any) {
    const query = event.target.value.toLowerCase();
    this.filteredDoctors = this.doctors.filter(doctor =>
      doctor.name.toLowerCase().includes(query) ||
      doctor.specialty.toLowerCase().includes(query)
    );
  }

  async openFacilitySelector() {
    const modal = await this.modalController.create({
      component: FacilitySelectorComponent,
      componentProps: {
        selectedFacility: this.selectedFacility
      },
      cssClass: 'facility-selector-modal',
      breakpoints: [0, 0.4, 0.6],
      initialBreakpoint: 0.4,
      handle: false
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.selectedFacility) {
        this.selectedFacility = result.data.selectedFacility;
      }
    });

    await modal.present();
  }

  async openSpecialtySelector() {
    const modal = await this.modalController.create({
      component: SpecialtySelectorComponent,
      componentProps: {
        selectedSpecialty: this.selectedSpecialty
      },
      cssClass: 'specialty-selector-modal',
      breakpoints: [0, 0.6, 0.8],
      initialBreakpoint: 0.6,
      handle: false
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.selectedSpecialty) {
        this.selectedSpecialty = result.data.selectedSpecialty;
      }
    });

    await modal.present();
  }

  // Add the missing openFacilityFilter method for compatibility
  async openFacilityFilter() {
    await this.openFacilitySelector();
  }

  // Add the missing openSpecialtyFilter method for compatibility
  async openSpecialtyFilter() {
    await this.openSpecialtySelector();
  }

  async openFilters() {
    const modal = await this.modalController.create({
      component: FiltersModalComponent,
      componentProps: {
        selectedFacility: this.selectedFacility,
        selectedSpecialty: this.selectedSpecialty,
        showFacilitySelector: this.selectedSegment === 'in-person' // Only show facility for in-person
      },
      cssClass: 'filters-modal'
    });

    modal.onDidDismiss().then((result) => {
      if (result.data) {
        // For in-person appointments, update both facility and specialty
        if (this.selectedSegment === 'in-person') {
          this.selectedFacility = result.data.facility;
          this.selectedSpecialty = result.data.specialty;
        } else {
          // For online appointments, only update specialty
          this.selectedSpecialty = result.data.specialty;
        }
      }
    });

    await modal.present();
  }

  selectDoctor(doctor: Doctor) {
    // Navigate to doctor details page with doctor data
    this.router.navigate(['/doctor', doctor.id], {
      state: {
        doctor: doctor,
        selectedDate: this.selectedDate,
        appointmentType: this.selectedSegment
      }
    });
  }

  selectDoctorOnline(doctor: Doctor) {
    // Navigate to doctor details page with online toggle selected
    this.router.navigate(['/doctor', doctor.id], {
      state: {
        doctor: doctor,
        selectedDate: this.selectedDate,
        appointmentType: 'online'
      }
    });
  }

  goBack() {
    // Navigate back or to previous page
    this.router.navigate(['/']);
  }
}
