import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { URLS } from '../constants/index';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  headers = new HttpHeaders({
    'Content-Type': 'application/json',
    skip: 'true',
    deviceType: 'ios',
  });
  name$: Subject<any> = new Subject();
  constructor(private httpClient: HttpClient) {}

  login(loginDetails: any): Observable<any> {
    return this.httpClient.post<any>(URLS.LOGIN, loginDetails, {
      headers: this.headers,
    });
  }

  authentication(token: any): Observable<any> {
    return this.httpClient.post<any>(URLS.AUTH, token);
  }

  // getPersona(payload: any): Observable<any> {
  //   return this.httpClient.post<any>(URLS.GEN_TOKEN_PERSONA, payload, {
  //     headers: this.headers,
  //   });
  // }
  getPersona(payload: any): Observable<any> {
    return this.httpClient.post<any>(URLS.GEN_TOKEN_PERSONA, payload);
  }
  getDashboardDetails(): Observable<any> {
    return this.httpClient.get<any>(URLS.GET_DASHBOARD_DETAILS);
  }
  getMarketingBanner(): Observable<any> {
    return this.httpClient.get<any>(URLS.MARKETING_BANNER);
  }
}
