<ion-header class="filters-header">
  <ion-toolbar class="filters-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="dismiss()">
        <ion-icon name="chevron-back" color="light"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="filters-title">Filters</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="filters-content">
  <div class="filters-container">
    <!-- Facility Section -->
    <div class="filter-section">
      <div class="filter-label">Facility</div>
      <div class="filter-dropdown" (click)="openFacilitySelector()">
        <span class="dropdown-text">{{ selectedFacility }}</span>
        <ion-icon name="chevron-down" class="dropdown-icon"></ion-icon>
      </div>
    </div>

    <!-- Specialty Section -->
    <div class="filter-section">
      <div class="filter-label">Speciality</div>
      <div class="filter-dropdown" (click)="openSpecialtySelector()">
        <span class="dropdown-text">{{ selectedSpecialty }}</span>
        <ion-icon name="chevron-down" class="dropdown-icon"></ion-icon>
      </div>
    </div>
  </div>

  <!-- Apply Button -->
  <div class="apply-button-container">
    <ion-button 
      expand="block" 
      class="apply-button"
      (click)="applyFilters()">
      Apply
    </ion-button>
  </div>
</ion-content>
