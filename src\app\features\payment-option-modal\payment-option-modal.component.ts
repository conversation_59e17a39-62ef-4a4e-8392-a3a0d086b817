import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonContent,
  IonButton,
  IonIcon,
  ModalController
} from '@ionic/angular/standalone';
// No longer need ionicons for custom SVG

@Component({
  selector: 'app-payment-option-modal',
  templateUrl: './payment-option-modal.component.html',
  styleUrls: ['./payment-option-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonButton,
    IonIcon
  ]
})
export class PaymentOptionModalComponent {

  constructor(private modalController: ModalController) {
    // No icon setup needed for custom SVG
  }

  useInsurance() {
    this.modalController.dismiss({
      paymentMethod: 'insurance'
    });
  }

  useSelfPay() {
    this.modalController.dismiss({
      paymentMethod: 'self-pay'
    });
  }
}
