import { Injectable } from '@angular/core';
import { LoadingController } from '@ionic/angular';
@Injectable({
  providedIn: 'root',
})
export class LoaderService {
  loading: any;

  constructor(public loadingCtrl: LoadingController) {}
   async presentLoader(text: any) {
    this.loading = await this.loadingCtrl.create({
      message: text,
      mode: 'ios',
    });

    this.loading.present();
  }

  dismissLoader() {
    this.loadingCtrl.dismiss();
  }
}
