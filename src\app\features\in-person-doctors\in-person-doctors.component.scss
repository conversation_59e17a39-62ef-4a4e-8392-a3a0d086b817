// Search Section
.search-section {
  padding: 0 16px 16px 16px;
  background: transparent;

  .custom-searchbar {
    --background: rgba(255, 255, 255, 0.08);
    --color: #ffffff;
    --placeholder-color: rgba(255, 255, 255, 0.6);
    --icon-color: rgba(255, 255, 255, 0.6);
    --border-radius: 12px;
    --box-shadow: none;
    --padding-start: 0px;
    --padding-end: 0px;
    --padding-top: 0px;
    --padding-bottom: 0px;

    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border: 0px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    margin: 0;
    padding: 0 !important;

    &.sc-ion-searchbar-md-h {
      padding: 0 !important;
    }

    .searchbar-input {
      font-size: 14px;
      font-weight: 400;
      padding: 0 !important;
    }

    .searchbar-input-container {
      padding: 0 !important;
    }
  }
}

// Doctor List - Matching Online Doctor Card Styling
.doctor-list {
  padding: 0 16px 32px 16px;
  background: transparent;

  .doctor-card {
    // Main container matching Figma VCP Appointment
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
    gap: 8px;
    width: 100%;
    height: 136px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 8px;
    margin-bottom: 24px;
    cursor: pointer;

    .doctor-card-content {
      // Frame 1948760707 - Main content container
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0px;
      gap: 8px;
      width: 100%;
      height: 104px;

      .doctor-main-info {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 104px;

        .doctor-left-section {
          // Frame 1948760718
          display: flex;
          flex-direction: row;
          align-items: flex-start;
          padding: 0px;
          gap: 8px;
          width: 227px;
          height: 104px;

          .doctor-avatar {
            // Frame 1948760715 + Ellipse 18
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 8px;
            width: 40px;
            height: 40px;

            img {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
            }
          }

          .doctor-details {
            // Frame 1948760716
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;
            gap: 16px;
            width: 179px;
            height: 104px;

            .doctor-name-specialty {
              // Frame 1948761579
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              padding: 0px;
              gap: 8px;
              width: 179px;
              height: 64px;

              .doctor-name {
                // Dr. Abdur Raheem
                min-width: 132px;
                height: 24px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 500;
                font-size: 16px;
                line-height: 24px;
                display: flex;
                align-items: center;
                color: #FCFCFC;
                margin: 0;
                flex: 1;
              }

              .doctor-specialty {
                // General Physician
                width: 96px;
                height: 16px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                color: #9E9FA9;
                margin: 0;
                margin-top: -12px;
              }
            }

            .doctor-languages {
              // Frame 1948760741
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              padding: 0px;
              gap: 4px;
              width: 102px;
              height: 16px;

              ion-icon {
                width: 16px;
                height: 16px;
                color: #FCFCFC;
              }

              span {
                // English, Arabic
                min-width: 82px;
                height: 16px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 500;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                color: #FCFCFC;
                flex: 1;
              }
            }

            .doctor-availability {
              // Frame 1000008826
              display: flex;
              flex-direction: row;
              align-items: center;
              padding: 0px;
              gap: 4px;
              width: 240px;
              height: 24px;

              .available-text {
                // Earliest available at:
                width: 109px;
                height: 16px;
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                color: #9E9FA9;
                margin-bottom: 15px;
              }

              .time-chip {
                // Selector
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 6px 2px;
                gap: 8px;
                width: 66px;
                height: 24px;
                background: rgba(255, 255, 255, 0.25);
                border: 1px solid #FFFFFF;
                border-radius: 8px;
                margin-bottom: 15px;

                // unit text
                font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-style: normal;
                font-weight: 500;
                font-size: 12px;
                line-height: 16px;
                display: flex;
                align-items: center;
                text-align: center;
                color: #FCFCFC;
              }
            }
          }
        }

        .doctor-right-section {
          // Frame 1948761173
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: flex-end;
          padding: 0px;
          width: 57px;
          height: 104px;

          .doctor-rating {
            // Frame 1948760705
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 0px;
            gap: 2px;
            width: 57px;
            height: 24px;

            ion-icon {
              width: 12px;
              height: 12px;
              font-size: 12px;
              color: #FFA800;
              display: flex;
              align-items: center;
              line-height: 16px;
              vertical-align: top;
              margin-top: -3.2px;
              border-radius: 2px;
              filter: drop-shadow(0 0 1px rgba(255, 168, 0, 0.3));
            }

            .rating-text {
              // 4.9 (40)
              width: 39px;
              height: 16px;
              font-family: 'DINOffcPro', 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              font-style: normal;
              font-weight: 400;
              font-size: 12px;
              line-height: 16px;
              display: flex;
              align-items: center;
              color: #9E9FA9;
            }
          }

          .arrow-icon {
            width: 24px;
            height: 24px;
            color: #FCFCFC;
          }
        }
      }
    }
  }
}
