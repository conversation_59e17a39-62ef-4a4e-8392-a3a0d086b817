import {
  HttpErrorR<PERSON>po<PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, finalize, switchMap, filter, take } from 'rxjs/operators';
import { HelperService } from '../helpers/helper.service';
import { ToastService } from '../helpers/toast.service';
import { ClientConfigService } from '../helpers/client-config.service';
@Injectable({
  providedIn: 'root',
})
export class TokenInterceptor implements HttpInterceptor {
  constructor(
    private router: Router,
    private helper: HelperService,
    private toast: ToastService,
    private performance: Performance,
    private clientconfig:ClientConfigService
  ) {}

  azureId: any;
  'clientid': string | null = null;
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null
  );

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<any> {
    if (request.headers.get('skip')) {
      const req = request.clone({
        setHeaders: {},
      });
      return next.handle(req);
    }

    const token: any = this.helper.getToken();
    request = this.addAuthHeader(request, token);
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        console.log(request.url.includes('refresh-token'));

        if (error.status === 401 && request.url.includes('refresh-token')) {
          this.logout();
        }
        if (error.status === 401) {
          if (!this.isRefreshing) {
            this.isRefreshing = true;
            this.refreshTokenSubject.next(null);
            this.toast.presentToast('refresh your accessibility ');
            //alert('refreshing token');

            return this.helper.getNewAccessToken().pipe(
              switchMap((newToken: any) => {
                console.log(newToken);

                this.isRefreshing = false;
                this.refreshTokenSubject.next(newToken);
                this.helper.setToken(
                  newToken.data.access_token,
                  newToken.data.refresh_token
                );
                const token = {
                  accessToken: newToken.data.access_token,
                  refreshToken: newToken.data.refresh_token,
                };
                return next.handle(this.addAuthHeader(request, token));
              }),
              catchError(refreshError => {
                return throwError(refreshError);
              }),
              finalize(() => {
                this.isRefreshing = false;
              })
            );
          } else {
            return this.refreshTokenSubject.pipe(
              filter(token => token !== null),
              take(1),
              switchMap(incomingtoken => {
                const token = {
                  accessToken: incomingtoken.data.access_token,
                  refreshToken: incomingtoken.data.refresh_token,
                };
                return next.handle(this.addAuthHeader(request, token));
              })
            );
          }
        }
        return throwError(error);
      }),
      finalize(() => {
      })
    );
  }

  logout() {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/splash-screen']);
    this.toast.presentToast('logging you out,another user using your user');

    //  alert('logout');
  }
  setClientId(clientId: string | null): void {
    this['clientid'] = clientId;
  }

  getClientId(): string | null {
    return this['clientid'];
  }
  addAuthHeader(request: HttpRequest<any>, token: any) {
    const skipIntercept = request.headers.has('skip');
    const clientId = localStorage.getItem('clientId');
  if (clientId) {
    this.setClientId(clientId); 
  }
    if (skipIntercept) {
      request = request.clone({
        headers: request.headers.delete('skip'),
      });
    }
  //  const clientIdForHeader = ''; 
  //  const clientNameForHeader = 'DAMAN'; 

    const clientIdForHeader = this.clientconfig.getClientIDSnapshot() || this.getClientId() || ''; 
    const clientNameForHeader = this.clientconfig.getClientSnapshot() || 'DAMAN'; 
const clientLanguage=this.clientconfig.getClientLanguageSnapshot() || 'en' ;
    return request.clone({
      setHeaders: {
        Authorization: `Bearer ${token.accessToken}`,
        channel: clientNameForHeader,
        clientid:clientIdForHeader,
       'accept-language':clientLanguage

      },
    });
  }
}
