<!-- Search Section -->
<div class="search-section" *ngIf="shouldShowDoctors()">
  <ion-searchbar 
    [(ngModel)]="searchTerm"
    (ionInput)="onSearchInput()"
    placeholder="Search for doctors"
    class="custom-searchbar">
  </ion-searchbar>
</div>

<!-- Doctor List -->
<div class="doctor-list" *ngIf="shouldShowDoctors()">
  <div *ngFor="let doctor of filteredDoctors" class="doctor-card" (click)="selectDoctor(doctor)">
    <div class="doctor-card-content">
      <div class="doctor-main-info">
        <div class="doctor-left-section">
          <div class="doctor-avatar">
            <img src="assets/images/doctor-image.jpg" [alt]="doctor.name">
          </div>
          <div class="doctor-details">
            <div class="doctor-name-specialty">
              <h3 class="doctor-name">{{ doctor.name }}</h3>
              <p class="doctor-specialty">{{ doctor.specialty }}</p>
            </div>
            <div class="doctor-languages">
              <ion-icon name="language"></ion-icon>
              <span>{{ doctor.languages }}</span>
            </div>
            <div class="doctor-availability">
              <span class="available-text">Earliest available at:</span>
              <div class="time-chip">{{ doctor.earliestTime }}</div>
            </div>
          </div>
        </div>
        <div class="doctor-right-section">
          <div class="doctor-rating">
            <ion-icon name="star"></ion-icon>
            <span class="rating-text">{{ doctor.rating }} ({{ doctor.ratingCount }})</span>
          </div>
          <ion-icon name="chevron-forward" class="arrow-icon"></ion-icon>
        </div>
      </div>
    </div>
  </div>
</div>
