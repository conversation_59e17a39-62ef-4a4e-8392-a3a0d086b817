import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DarkModeService {
  private isDarkModeSubject = new BehaviorSubject<boolean>(false); // Default: light mode
  isDarkMode$ = this.isDarkModeSubject.asObservable();

  constructor() {
    // Initialize dark mode based on localStorage or system preference
    const savedDarkMode = localStorage.getItem('darkmode');
    // const prefersDark = window.matchMedia(
    //   '(prefers-color-scheme: dark)'
    // ).matches;
    const isDarkMode = savedDarkMode === '1';
    this.isDarkModeSubject.next(isDarkMode);
  }

  toggleDarkMode(vendor:any,isDarkMode: any) {
    console.log(vendor);
    
    this.isDarkModeSubject.next(isDarkMode);
if(vendor == 'DAMAN' && isDarkMode){
  document.body.setAttribute('theme', 'daman-dark');
  localStorage.setItem('darkmode', '1');
}
else if(vendor == 'DAMAN' && !isDarkMode){
  document.body.setAttribute('theme', 'daman-light');
  localStorage.setItem('darkmode', '0');
}else if(vendor == 'PURA' && isDarkMode){
  document.body.setAttribute('theme', 'pura-dark');
  localStorage.setItem('darkmode', '1');
}else if(vendor == 'PURA' && !isDarkMode){
  document.body.setAttribute('theme', 'pura-light');
  localStorage.setItem('darkmode', '0');
}
    // Update body attribute and localStorage
    // if (isDarkMode) {
    //   document.body.setAttribute('theme', 'dark');
    //   localStorage.setItem('darkmode', '1');
    // } else {
    //   document.body.removeAttribute('theme');
    //   localStorage.setItem('darkmode', '0');
    // }
  }
}
