import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  IonContent,
  IonIcon
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { videocam, mic, headset, volumeOff } from 'ionicons/icons';

@Component({
  selector: 'app-waiting',
  templateUrl: './waiting.page.html',
  styleUrls: ['./waiting.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonIcon
  ]
})
export class WaitingPage implements OnInit, OnDestroy {
  private timeoutId: any;

  constructor(private router: Router) {
    addIcons({
      videocam,
      mic,
      headset,
      volumeOff
    });
  }

  ngOnInit() {
    // Start 5-second timeout to navigate back to main page
    this.timeoutId = setTimeout(() => {
      this.goBack();
    }, 5000);
  }

  ngOnDestroy() {
    // Clear timeout if component is destroyed before timeout completes
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  goBack() {
    // Navigate to main page (online-doctor)
    this.router.navigate(['/online-doctor']);
  }
}
