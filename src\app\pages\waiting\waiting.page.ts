import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  IonContent,
  IonIcon
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { videocam, mic, headset, volumeOff } from 'ionicons/icons';

@Component({
  selector: 'app-waiting',
  templateUrl: './waiting.page.html',
  styleUrls: ['./waiting.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonIcon
  ]
})
export class WaitingPage implements OnInit {

  constructor(private router: Router) {
    addIcons({
      videocam,
      mic,
      headset,
      volumeOff
    });
  }

  ngOnInit() {
    // You can add any initialization logic here
    // For example, start a timer or connect to consultation service
  }

  goBack() {
    this.router.navigate(['/book-appointment']);
  }
}
