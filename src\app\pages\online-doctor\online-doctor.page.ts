import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonSearchbar,
  IonCard,
  IonCardContent,
  IonItem,
  IonLabel,
  IonAvatar,
  IonBadge
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { 
  chevronBack, 
  search, 
  star, 
  calendar, 
  laptop, 
  medkit, 
  document, 
  time, 
  ellipsisHorizontal,
  chevronForward
} from 'ionicons/icons';

@Component({
  selector: 'app-online-doctor',
  templateUrl: './online-doctor.page.html',
  styleUrls: ['./online-doctor.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonSearchbar,
    IonCard,
    IonCardContent,
    IonItem,
    IonLabel,
    IonAvatar,
    IonBadge
  ]
})
export class OnlineDoctorPage implements OnInit {
  searchQuery: string = '';

  // Mock upcoming appointment data
  upcomingAppointment = {
    time: '6:00 PM',
    status: 'Confirmed',
    doctor: {
      name: 'Dr. Abdur Raheem',
      specialty: 'General Physician',
      rating: 4.9,
      ratingCount: 40,
      avatar: 'https://via.placeholder.com/48x48/3a4a5c/ffffff?text=DR'
    },
    date: 'Tue, 16 Jul 2024',
    type: 'Online'
  };

  // Explore services data
  exploreServices = [
    { icon: 'FirstAid.svg', title: 'Lab Orders', color: '#FF6B6B', isSvgIcon: true },
    { icon: 'pill.svg', title: 'Prescriptions', color: '#ACD6EE', isSvgIcon: true },
    { icon: 'sick-leave.svg', title: 'Sick leaves', color: '#45B7D1', isSvgIcon: true },
    { icon: 'follow-up.svg', title: 'Follow-up', color: '#96CEB4', isSvgIcon: true },
    { icon: 'history.svg', title: 'History', color: '#FECA57', isSvgIcon: true },
    { icon: 'more.svg', title: 'More', color: '#A8A8A8', isSvgIcon: true }
  ];

  constructor(private router: Router) {
    addIcons({
      chevronBack,
      search,
      star,
      calendar,
      laptop,
      medkit,
      document,
      time,
      ellipsisHorizontal,
      chevronForward
    });
  }

  ngOnInit() {
    // Initialize page
  }

  goBack() {
    // Navigate back to previous page or main menu
    this.router.navigate(['/']);
  }

  onSearchInput(event: any) {
    this.searchQuery = event.detail.value;
    // Implement search functionality here
  }

  bookAppointment() {
    // Navigate to book appointment page
    this.router.navigate(['/book-appointment']);
  }

  viewUpcomingAppointment() {
    // Navigate to appointment details or doctor page
    this.router.navigate(['/doctor', 1], {
      state: {
        doctor: this.upcomingAppointment.doctor,
        selectedDate: '16/7',
        appointmentType: 'online'
      }
    });
  }

  onServiceClick(service: any) {
    // Handle service clicks
    console.log('Service clicked:', service.title);
    
    // For now, navigate to book appointment for all services
    // In a real app, you'd navigate to specific service pages
    switch (service.title) {
      case 'Lab Orders':
      case 'Prescriptions':
      case 'Sick leaves':
      case 'Follow-up':
      case 'History':
      case 'More':
        // Navigate to respective service pages
        // For demo, just log the action
        break;
      default:
        break;
    }
  }
}
