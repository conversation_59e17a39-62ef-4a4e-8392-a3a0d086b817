.facility-selector-content {
  --background: #031934;
  --color: #ffffff;
  background: #031934;
  --padding-bottom: 0;
  --padding-top: 0;
  --padding-start: 0;
  --padding-end: 0;
}

.facility-selector-container {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: 100vh;
  
  .selector-header {
    padding: 16px 16px 12px 16px;

    h3 {
      color: #ffffff;
      font-size: 18px;
      font-weight: 500;
      margin: 0;
    }
  }
  
  .facility-options {
    padding: 8px 0 0 0;

    .facility-option {
      padding: 16px;
      color: #ffffff;
      font-size: 16px;
      font-weight: 400;
      cursor: pointer;
      transition: background-color 0.2s ease;
      position: relative;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      &:not(:last-of-type)::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 15px;
        right: 15px;
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
      }
    }

    .coming-soon {
      padding: 12px 16px 8px 16px;
      color: rgba(255, 255, 255, 0.5);
      font-size: 16px;
      font-style: normal;
    }
  }
  
  .close-button-container {
    padding: 8px 16px 16px 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.0);

    .close-button {
      --background: rgba(255, 255, 255, 0.2);
      --background-activated: rgba(255, 255, 255, 0.3);
      --background-hover: rgba(255, 255, 255, 0.25);
      --color: #ffffff;
      --border-radius: 8px;
      --padding-top: 12px;
      --padding-bottom: 12px;
      --text-transform: none;

      font-size: 14px;
      font-weight: 600;
      margin: 0;
      height: 44px;
      text-transform: none;
    }
  }
}
