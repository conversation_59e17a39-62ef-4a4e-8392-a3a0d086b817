// Appointment Confirmation Page Styles (based on appointment confirmation modal)
.confirmation-header {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  margin-top: 28px;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
  position: relative;

  // Black overlay from top of modal to bottom of toolbar
  &::before {
    content: '';
    position: absolute;
    top: -28px; // Extend up to cover the margin-top area
    left: 0;
    right: 0;
    bottom: 0; // This will go to the bottom of the header including toolbar
    background: rgba(0, 0, 0, 0.04);
    z-index: 0;
  }

  .confirmation-toolbar {
    --background: transparent;
    --color: #ffffff;
    --border-width: 0;
    --border-style: solid;
    --border-color: #16425d;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --padding-start: 12px;
    --padding-end: 16px;
    background: transparent;
    min-height: 56px;
    position: relative;
    z-index: 1;

    .confirmation-title {
      --color: #ffffff;
      font-size: 20px;
      font-weight: 500;
      padding: 0 0 0 0;
      margin-bottom: -6px;
    }

    ion-button {
      --color: #ffffff;
      --background: transparent;
      position: relative;
      z-index: 10;
      pointer-events: auto;

      ion-icon {
        font-size: 24px;
      }
    }

    .back-button {
      --color: #ffffff;
      --background: transparent;
      position: relative;
      z-index: 10;
      pointer-events: auto;
      min-width: 44px;
      min-height: 44px;
    }
  }
}

.confirmation-content {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  
  .confirmation-container {
    padding: 24px 16px 100px 16px; // Extra bottom padding for button
    
    // Appointment Date & Time
    .appointment-datetime {
      margin-bottom: 24px;
      
      .datetime-text {
        color: #ffffff;
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        line-height: 1.4;
        margin-top: -6px;
      }
    }

    // Doctor Info Section
    .doctor-info-section {
      margin-bottom: 15px;
      
      .specialty-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        font-weight: 400;
        margin-bottom: -2px;
      }
      
      .doctor-name-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .doctor-name {
          color: #ffffff;
          font-size: 16px;
          font-weight: 400;
          margin: 0;
        }
        
        .doctor-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    // Appointment Details
    .appointment-details {
      margin-bottom: 20px;

      .detail-row {
        margin-bottom: 8px;

        .detail-label {
          color: rgba(255, 255, 255, 0.7);
          font-size: 16px;
          font-weight: 400;
          display: block;
        }

        .detail-value {
          color: #ffffff;
          font-size: 16px;
          font-weight: 400;
          display: block;

          &.appointment-type {
            font-weight: 400;
            margin-top: -2px;
          }
        }
      }
    }

    // Payment Section
    .payment-section {
      margin-bottom: 32px;

      .payment-label {
        color: rgba(255, 255, 255, 1);
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 16px;
      }

      .payment-toggle-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-top: 12px;

        .payment-toggle-buttons {
          display: flex;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 24px;
          padding: 2px;
          width: auto;
          position: relative;
          transition: all 0.2s ease;

          .toggle-button {
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            font-size: 16px;
            font-weight: 400;
            padding: 8px 20px;
            border-radius: 24px;
            transition: all 0.2s ease;
            position: relative;
            z-index: 2;
            height: 35px;
            min-width: 100px;
            cursor: pointer;
            outline: none;

            &.active {
              color: #ffffff;
              font-weight: 500;
              background: rgba(255, 255, 255, 0.25);
              border: solid 1px rgba(255, 255, 255, 1);
            }
          }
        }
      }
    }

    // Consultation Fee
    .consultation-fee-section {
      margin-bottom: 16px;

      .fee-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 2px;
      }

      .fee-amount {
        color: #ffffff;
        font-size: 16px;
        font-weight: 400;
        margin-top: -15px;
      }

      &:last-of-type {
        margin-bottom: 32px;
      }
    }

    // Notice Section
    .notice-section {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      
      .notice-icon {
        margin-top: 2px;
        flex-shrink: 0;

        .alert-icon {
          width: 20px;
          height: 20px;
          display: block;
        }
      }
      
      .notice-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
        font-weight: 400;
        line-height: 1.4;
      }
    }
  }
  
  // Confirm Button (matching book appointment button styling)
  .confirm-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: transparent;

    .confirm-button {
      --background: #004AD9;
      --background-activated: #0056CC;
      --background-hover: #0056CC;
      --color: #ffffff;
      --border-radius: 8px;
      --padding-top: 16px;
      --padding-bottom: 16px;
      --text-transform: none;

      font-size: 18px;
      font-weight: 500;
      height: 60px;
      margin: 0;
      margin-bottom: 20px;
      text-transform: none;
    }
  }
}
