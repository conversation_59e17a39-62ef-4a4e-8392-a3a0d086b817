// Online Doctor Landing Page Styles
.online-doctor-header {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  margin-top: 28px;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
  position: relative;

  // Black overlay from top of modal to bottom of toolbar
  &::before {
    content: '';
    position: absolute;
    top: -28px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.04);
    z-index: 0;
  }

  .online-doctor-toolbar {
    --background: transparent;
    --color: #ffffff;
    --border-width: 0;
    --border-style: solid;
    --border-color: #16425d;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --padding-start: 12px;
    --padding-end: 16px;
    background: transparent;
    min-height: 56px;
    position: relative;
    z-index: 1;

    .online-doctor-title {
      --color: #ffffff;
      font-size: 20px;
      font-weight: 500;
      padding: 0 0 0 0;
      margin-bottom: -6px;
    }

    ion-button {
      --color: #ffffff;
      --background: transparent;

      ion-icon {
        font-size: 24px;
      }
    }
  }
}

.online-doctor-content {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  
  .online-doctor-container {
    padding: 8px 16px 16px 16px;

    // Search Section
    .search-section {
      margin-top: 0px;
      margin-bottom: 20px;
      display: flex;
      justify-content: center;

      .search-container {
        width: 336px;
        max-width: 336px;

        .custom-searchbar {
          padding: 0px;
          --background: rgba(255, 255, 255, 0.1) !important;
          --color: #ffffff !important;
          --placeholder-color: rgba(255, 255, 255, 0.6) !important;
          --icon-color: rgba(255, 255, 255, 0.6) !important;
          --border-radius: 8px !important;
          --box-shadow: none !important;
          --padding-start: 0px !important;
          --padding-end: 0px !important;
          --padding-top: 0px !important;
          --padding-bottom: 0px !important;
          width: 100% !important;
          height: 56px !important;
          min-height: 56px !important;

          input.searchbar-input {
            font-size: 14px !important;
            line-height: 14px !important;
            color: #ffffff !important;
            background: transparent !important;
            padding: 0 !important;
            margin: 0 !important;
            height: auto !important;
            padding-inline-start: 45px !important;
            padding-inline-end: 0px !important;
            vertical-align: bottom !important;
            display: flex !important;
            align-items: flex-end !important;

            &::placeholder {
              color: rgba(255, 255, 255, 0.6) !important;
              font-size: 14px !important;
              opacity: 1 !important;
            }
          }

          // Target the specific Ionic class to remove padding
          .searchbar-input.sc-ion-searchbar-md {
            padding: 0 !important;
            margin: 0 !important;
            padding-inline-start: 45px !important;
            padding-inline-end: 0px !important;
          }

          .searchbar-input-container {
            padding: 0 !important;
            margin: 0 !important;
            height: auto !important;
            min-height: auto !important;
          }
        }
      }
    }

    // Consultation Card Section
    .consultation-card-section {
      margin-bottom: 20px;

      .consultation-card {
        --background: #5828B1;
        --color: #ffffff;
        border-radius: 8px;
        margin: 0 auto; // Center the card
        box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
        height: 128px; // Fixed height to exact specification
        width: 345px; // Fixed width to exact specification
        max-width: 345px;

        .consultation-card-content {
          padding: 20px 20px 0 20px;
          position: relative;
          overflow: visible;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .consultation-text {
            margin-bottom: 12px;

            h2 {
              color: #ffffff;
              font-size: 16px;
              font-weight: 500;
              line-height: 1.3;
              margin: 0;
              max-width: 100%;
            }
          }

          .consultation-action {
            margin-top: -28px;

            .book-appointment-btn {
              --background: rgba(255, 255, 255, 0.2);
              --background-activated: rgba(255, 255, 255, 0.3);
              --background-hover: rgba(255, 255, 255, 0.3);
              --color: #ffffff;
              --border-radius: 20px;
              --padding-start: 12px;
              --padding-end: 10px;
              --padding-top: 0px;
              --padding-bottom: 0px;

              font-size: 12px;
              font-weight: 400;
              width: 150px;
              height: 10px;
              text-transform: none;
              transform: translateY(-10px);
              position: relative;

              ion-icon {
                margin-left: 6px;
                font-size: 14px;
              }
            }
          }

          .consultation-icon {
            position: absolute;
            right: 20px;
            bottom: -30px;
            z-index: 1;

            .consultation-img {
              width: 120px;
              height: 120px;
              opacity: 0.9;
              display: block;
            }
          }

        }
      }
    }

    // Upcoming Appointments Section
    .upcoming-section {
      margin-bottom: 24px;

      .section-title {
        color: #ffffff;
        font-size: 17px;
        font-weight: 500;
        margin: 0 0 12px 0;
        margin-left: 12px;
      }

      .appointment-card {
        // Main container matching Figma VCP Appointment (consistent with other doctor cards)
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;
        gap: 8px;
        width: 336px; // Same as 3 columns: (104px * 3) + (12px gap * 2) = 336px
        max-width: 336px;
        margin: 0 auto; // Center the card
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.25);
        border-radius: 8px;
        cursor: pointer;

        .appointment-time-status {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          margin-bottom: 0px;
          padding-right: 0px; // Ensure no padding on the right

          .time {
            color: #ffffff;
            font-size: 12px;
            font-weight: 400;
          }

          .status-badge {
            --background: transparent;
            --color: #10B981;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 0px;
            border-radius: 12px;
            margin-right: 0px; // Align to the right edge

            &.confirmed {
              --background: transparent;
              --color: #10B981;
            }
          }
        }

        .appointment-divider {
          width: 100%;
          height: 1px;
          background: rgba(255, 255, 255, 0.2);
          margin-top: 1px;
          margin-bottom: 4px;
        }

        .doctor-card-content {
          // Frame 1948760707 - Main content container
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          padding: 0px;
          gap: 8px;
          width: 100%;

          .doctor-main-info {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            .doctor-left-section {
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              padding: 0px;
              gap: 12px;
              flex: 1;

              .doctor-avatar {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                overflow: hidden;
                flex-shrink: 0;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              .doctor-details {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                padding: 0px;
                gap: 8px;
                flex: 1;

                .doctor-name-specialty {
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                  padding: 0px;
                  gap: 4px;
                  width: 100%;

                  .doctor-name-rating {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    width: 100%;

                    .doctor-name {
                      font-family: 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                      font-style: normal;
                      font-weight: 500;
                      font-size: 16px;
                      line-height: 24px;
                      color: #FFFFFF;
                      margin: 0;
                    }

                    .doctor-rating {
                      display: flex;
                      align-items: center;
                      gap: 4px;
                      margin-left: 30px;

                      ion-icon {
                        color: #FFA800;
                        font-size: 12px;
                      }

                      .rating-text {
                        font-family: 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        font-style: normal;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 16px;
                        color: rgba(255, 255, 255, 0.6);
                      }
                    }
                  }

                  .doctor-specialty {
                    font-family: 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 16px;
                    color: rgba(255, 255, 255, 0.5);
                    margin: 0;
                  }
                }

                .appointment-info {
                  display: flex;
                  flex-direction: column;
                  gap: 4px;

                  .appointment-date,
                  .appointment-type {
                    display: flex;
                    align-items: center;
                    gap: 6px;

                    ion-icon {
                      color: rgba(255, 255, 255, 0.7);
                      font-size: 14px;
                    }

                    span {
                      font-family: 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                      font-style: normal;
                      font-weight: 400;
                      font-size: 12px;
                      line-height: 16px;
                      color: rgba(255, 255, 255, 0.8);
                    }
                  }
                }
              }
            }

            .doctor-right-section {
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 0px;

              .arrow-icon {
                color: rgba(255, 255, 255, 0.5);
                font-size: 16px;
              }
            }
          }
        }
      }
    }

    // Explore Services Section
    .explore-section {
      .section-title {
        color: #ffffff;
        font-size: 17px;
        font-weight: 500;
        margin: 0 0 12px 0;
        margin-left: 12px;
      }

      .services-grid {
        display: grid;
        grid-template-columns: repeat(3, 104px);
        gap: 12px;
        justify-content: center;
        justify-items: center;

        .service-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 6px;
          padding: 12px 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          border-radius: 12px;
          background-color: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.12);
          width: 104px;
          height: 80px;

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
          }

          .service-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 4px;
            background-color: transparent;

            .icon {
              color: #ffffff;
              font-size: 24px;

              &.svg-icon {
                width: 24px;
                height: 24px;
                filter: none; // Keep original colors
                object-fit: contain;

                // Specific color overlay for pill icon
                &[src*="pill.svg"] {
                  filter: brightness(0) saturate(100%) invert(85%) sepia(18%) saturate(1234%) hue-rotate(174deg) brightness(95%) contrast(91%);
                  // This filter converts any color to #ACD6EE
                }
              }
            }
          }

          .service-title {
            color: #ffffff;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
          }
        }
      }
    }
  }
}
