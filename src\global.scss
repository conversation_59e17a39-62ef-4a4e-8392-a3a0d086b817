/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';

/**
 * Custom Global Styles
 * -----------------------------------------------------
 */

// Dark blue gradient background to match reference design
ion-app {
  background: linear-gradient(180deg, #1a2332 0%, #0f1419 100%);
  min-height: 100vh;
}

// Global text colors for dark theme with improved contrast
body {
  --ion-background-color: #1a2332;
  --ion-background-color-rgb: 26, 35, 50;
  --ion-text-color: #ffffff;
  --ion-text-color-rgb: 255, 255, 255;

  // Custom color variables for consistent theming
  --ion-color-primary: #1a2332;
  --ion-color-primary-rgb: 26, 35, 50;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #171f2c;
  --ion-color-primary-tint: #313e48;

  // Secondary colors for better contrast
  --ion-color-secondary: #3a4a5c;
  --ion-color-secondary-rgb: 58, 74, 92;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;

  // Text colors for better hierarchy
  --ion-color-medium: #8a9ba8;
  --ion-color-medium-rgb: 138, 155, 168;
  --ion-color-light: #ffffff;
  --ion-color-light-rgb: 255, 255, 255;
}

// Support for backdrop-filter (glassmorphism)
* {
  -webkit-backdrop-filter: inherit;
  backdrop-filter: inherit;
}

// Enhanced typography
body, * {
  font-family: 'DINOffcPro', sans-serif !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Smooth transitions for interactive elements
* {
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

// Hide scrollbars but keep functionality
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

// Improve button and interactive element styling
ion-button {
  --transition: all 0.2s ease-in-out;
  --text-transform: none;
  text-transform: none;
}

// Fix text-transform for segment buttons
ion-segment-button {
  --text-transform: none !important;
  text-transform: none !important;

  ion-label {
    text-transform: none !important;
  }
}

ion-card {
  --transition: all 0.2s ease-in-out;
}

// Global toolbar transparency override
ion-header ion-toolbar {
  --background: transparent !important;
  --ion-toolbar-background: transparent !important;
  --ion-color-step-50: transparent !important;
  --ion-background-color-step-50: transparent !important;
  background: transparent !important;
  background-color: transparent !important;

  .toolbar-container {
    background: transparent !important;
    background-color: transparent !important;
  }
}

/* Font-face declarations for all fonts in assets/fonts */
@font-face {
  font-family: 'DINOffcPro';
  src: url('./assets/fonts/DINOffcPro.woff2') format('woff2'),
       url('./assets/fonts/DINOffcPro.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINOffcPro';
  src: url('./assets/fonts/DINOffcPro-Bold.woff2') format('woff2'),
       url('./assets/fonts/DINOffcPro-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINOffcPro';
  src: url('./assets/fonts/DINOffcPro-Light.woff2') format('woff2'),
       url('./assets/fonts/DINOffcPro-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINOffcPro';
  src: url('./assets/fonts/DINOffcPro.woff2') format('woff2'),
       url('./assets/fonts/DINOffcPro.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINOffcPro';
  src: url('./assets/fonts/DINOffcPro-Medi.woff2') format('woff2'),
       url('./assets/fonts/DINOffcPro-Medi.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINOffcPro';
  src: url('./assets/fonts/DINOffcPro-Black.woff2') format('woff2'),
       url('./assets/fonts/DINOffcPro-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

* {
  box-shadow: none !important;
  text-shadow: none !important;
}

// Filters Modal Styles
.filters-modal {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;

  .modal-wrapper {
    --background: linear-gradient(180deg, #023753 0%, #020621 100%);
    --color: #ffffff;
    background: linear-gradient(180deg, #023753 0%, #020621 100%);
  }

  ion-backdrop {
    --background: rgba(0, 0, 0, 0.6);
  }
}

// Doctor Details Modal Styles
.doctor-details-modal {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;

  .modal-wrapper {
    --background: linear-gradient(180deg, #023753 0%, #020621 100%);
    --color: #ffffff;
    background: linear-gradient(180deg, #023753 0%, #020621 100%);
  }

  ion-backdrop {
    --background: rgba(0, 0, 0, 0.6);
  }
}

// Appointment Confirmation Modal Styles
.appointment-confirmation-modal {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;

  .modal-wrapper {
    --background: linear-gradient(180deg, #023753 0%, #020621 100%);
    --color: #ffffff;
    background: linear-gradient(180deg, #023753 0%, #020621 100%);
  }

  ion-backdrop {
    --background: rgba(0, 0, 0, 0.6);
  }
}

// Payment Option Modal Styles (bottom sheet)
.payment-option-modal {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;

  .modal-wrapper {
    --background: transparent;
    --color: #ffffff;
    background: transparent;
    border-radius: 24px 24px 0 0;
    margin: 0;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 50vh;
    min-height: 300px;
    overflow: hidden;
  }

  ion-backdrop {
    --background: rgba(0, 0, 0, 0.6);
  }
}

// Consultation Tips Modal Styles (bottom sheet)
.consultation-tips-modal {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%);
  --color: #ffffff;

  .modal-wrapper {
    --background: transparent;
    --color: #ffffff;
    background: transparent;
    border-radius: 24px 24px 0 0;
    margin: 0;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 70vh;
    min-height: 450px;
    overflow: hidden;
  }

  ion-backdrop {
    --background: rgba(0, 0, 0, 0.6);
  }
}

// Override Ionic select popover styles globally
ion-popover.select-popover,
ion-popover.custom-facility-popover {
  --background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
  --color: #ffffff !important;
  --box-shadow: none !important;
  --border-radius: 12px !important;
  --max-width: 280px !important;
  --width: 280px !important;

  ion-content {
    --background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
    background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
  }

  .popover-content {
    background: linear-gradient(180deg, #023753 0%, #020621 100%) !important;
    border-radius: 12px;
    padding: 0;
  }

  // Header styling
  .popover-header {
    padding: 16px 16px 12px 16px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: transparent !important;

    .popover-title {
      color: #ffffff !important;
      font-size: 16px !important;
      font-weight: 600 !important;
      margin: 0 !important;
    }
  }

  ion-radio-group {
    background: transparent !important;
    padding: 8px 0 !important;

    ion-item {
      --background: transparent !important;
      --color: #ffffff !important;
      --border-color: transparent !important;
      --inner-border-width: 0 !important;
      --border-width: 0 !important;
      --padding-start: 16px !important;
      --padding-end: 16px !important;
      --min-height: 48px !important;
      border: none !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
      background: transparent !important;

      &:last-child {
        border-bottom: none !important;
      }

      &:hover {
        --background: rgba(255, 255, 255, 0.05) !important;
        background: rgba(255, 255, 255, 0.05) !important;
      }

      &[disabled] {
        --color: rgba(255, 255, 255, 0.5) !important;
        --min-height: 36px !important;
        border-bottom: none !important;

        ion-label {
          color: rgba(255, 255, 255, 0.5) !important;
          font-size: 12px !important;
          font-style: italic !important;
        }
      }

      ion-label {
        color: #ffffff !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        padding: 12px 0 !important;
      }

      ion-radio {
        display: none !important;
      }
    }
  }
}

// Remove all default Ionic select styling
ion-select {
  --background: transparent !important;
  --color: #ffffff !important;
  --border-width: 0 !important;
  --border-style: none !important;
  --border-color: transparent !important;
  --box-shadow: none !important;
  --highlight-color-focused: transparent !important;
  --highlight-color-valid: transparent !important;
  --highlight-color-invalid: transparent !important;
  --padding-start: 0 !important;
  --padding-end: 0 !important;
  --padding-top: 0 !important;
  --padding-bottom: 0 !important;

  .select-text {
    color: #ffffff !important;
  }

  .select-icon {
    display: none !important;
  }
}

// Facility Selector Modal Styles
.facility-selector-modal,
.specialty-selector-modal {
  --background: #031934;
  --color: #ffffff;

  .modal-wrapper {
    --background: #031934;
    --color: #ffffff;
    background: #031934;
    border-radius: 12px 12px 0 0;
  }

  ion-backdrop {
    --background: rgba(0, 0, 0, 0.6);
  }
}

// Force override for searchbar padding
ion-searchbar input.searchbar-input,
.searchbar-input.sc-ion-searchbar-md,
.custom-searchbar input.searchbar-input,
.custom-searchbar .searchbar-input.sc-ion-searchbar-md {
  padding-inline-start: 35px !important;
  padding-inline-end: 0px !important;
  padding-left: 35px !important;
  padding-right: 0px !important;
}

// Position search icon closer to the left edge
ion-searchbar .searchbar-search-icon,
.searchbar-search-icon.sc-ion-searchbar-md,
.custom-searchbar .searchbar-search-icon {
  left: 12px !important;
  margin-left: 0 !important;
}


