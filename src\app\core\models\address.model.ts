export interface DeliveryAddress {
  addressIdEntity?: number | string;
  addressLineOne: string;
  addressLineTwo?: string;
  cityId?: string;
  cityName?: string;
  city?: any;
  deliveryFee?: string;
  longitude: string;
  latitude: string;
  googleAddress: string;
  googleLocation?: any;
  zipCode?: string | null;
  isPrimary?: boolean;
  isCustomType?: boolean;
  addressTypeId?: number;
  addressId?: number | string;
  addressType?: string;
  addressType_ar?: string;
  addressIconUrl?: string;
}

export interface AddEditAddressPayload {
  addressId?: string;
  addressLineOne: string;
  addressLineTwo?: string;
  addressType: string | number;
  city: string;
  googleLocation: string;
  isCustomType?: boolean;
  isPrimary: boolean;
  latitude: number | string;
  longitude: number | string;
  // userId: string;
  zipCode?: string | null;
}

export interface Prescription {
  id: number;
  fileName: string;
  filePath: string;
}

export interface InsurancePlan {
  id: number;
  planName: string;
}

export interface BottomSheetInputData {
  isEdit: boolean;
  address: DeliveryAddress;
}

export interface AddAddressResponse {
  createdAt: string;
  updatedAt: string;
  id: number;
  addressId: number | string;
  addressLineOne: string;
  addressLineTwo: string;
  longitude: string;
  latitude: string;
  googleAddress: string;
  zipCode: string | null;
  addressCity: number;
  addressDistrict: string;
  addressState: string;
  addressCountry: string;
  addressText: string;
  createdBy: number;
  updatedBy: number;
  addressUse: string | null;
  area: string | null;
  refillCity: string | null;
  state: string | null;
  country: string | null;
  addressPeriodStart: string | null;
  addressPeriodEnd: string | null;
  type: string | null;
}
