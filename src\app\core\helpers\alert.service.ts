import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ModalController } from '@ionic/angular';
import { CustomAlertComponent } from 'src/app/shared/components/presentational-components/custom-alert/custom-alert.component';
@Injectable({
  providedIn: 'root',
})
export class AlertService {
  constructor(
    private alert: AlertController,
    private modalCtrl: ModalController
  ) {}
  async presentAlert(
    header: string,
    imagePath: string,
    message: string,
    buttons?: any[]
  ) {
    const modal = await this.modalCtrl.create({
      component: CustomAlertComponent,
      componentProps: {
        header: header,
        imagePath: imagePath,
        message: message,
        buttons: buttons,
      },
      cssClass: 'custom-alert-modal',
    });
    modal.onDidDismiss().then((data: any) => {
      this.alert.dismiss();
    });

    await modal.present();
  }

  dismissAlert() {
    this.alert.dismiss();
  }
}
