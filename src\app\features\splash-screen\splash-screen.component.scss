ion-content {
  --background: transparent;

  .splash-container {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(
      --swiper-bullet-light-blue
    ); /* Backup background color */
  }
  .splash-container-pura {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pura-splash-dark {
    background: linear-gradient(180deg, #023753 0%, #020621 100%);
  }

  .pura-splash-light {
    background: linear-gradient(180deg, #f7f9fb 0%, #f7f9fb 100%);
  }
  .pura-icon-dark {
    color: #FFFFFF;
  }

  .pura-icon-light {
    color: #10317B !important;
  }
  .pura-text-dark {
    color: #FFFFFF !important;
  }

  .pura-text-light {
    color: #020621;
  }
  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }

  .logo {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    p {
      color: var(--heading-black-text);
      padding-top: 1rem;
    }
    img {
      width: 150px; /* Adjust logo size as needed */
    }
    i {
      color: var(--splash-logo-blue);
      font-size: 5rem;
    }
  }
}
