import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButtons,
  IonButton,
  IonIcon,
  IonItem,
  IonLabel,
  IonSelect,
  IonSelectOption,
  ModalController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { chevronBack, chevronDown } from 'ionicons/icons';
import { FacilitySelectorComponent } from '../facility-selector/facility-selector.component';
import { SpecialtySelectorComponent } from '../specialty-selector/specialty-selector.component';

@Component({
  selector: 'app-filters-modal',
  templateUrl: './filters-modal.component.html',
  styleUrls: ['./filters-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonButtons,
    IonButton,
    IonIcon,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption
  ]
})
export class FiltersModalComponent {
  @Input() selectedFacility: string = 'SEHA CLINIC Corniche Abu Dhabi';
  @Input() selectedSpecialty: string = 'General Physician';

  facilities = [
    'SEHA CLINIC Corniche',
    'Another CLINIC'
  ];

  specialties = [
    'General Physician',
    'Cardiologist',
    'Dermatologist',
    'Pediatrician',
    'Orthopedic',
    'Neurologist'
  ];

  constructor(private modalController: ModalController) {
    addIcons({
      chevronBack,
      chevronDown
    });
  }

  dismiss() {
    this.modalController.dismiss();
  }

  async openFacilitySelector() {
    const modal = await this.modalController.create({
      component: FacilitySelectorComponent,
      componentProps: {
        selectedFacility: this.selectedFacility
      },
      cssClass: 'facility-selector-modal',
      breakpoints: [0, 0.4, 0.6],
      initialBreakpoint: 0.4,
      handle: false
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.selectedFacility) {
        this.selectedFacility = result.data.selectedFacility;
      }
    });

    await modal.present();
  }

  async openSpecialtySelector() {
    const modal = await this.modalController.create({
      component: SpecialtySelectorComponent,
      componentProps: {
        selectedSpecialty: this.selectedSpecialty
      },
      cssClass: 'specialty-selector-modal',
      breakpoints: [0, 0.6, 0.8],
      initialBreakpoint: 0.6,
      handle: false
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.selectedSpecialty) {
        this.selectedSpecialty = result.data.selectedSpecialty;
      }
    });

    await modal.present();
  }

  applyFilters() {
    this.modalController.dismiss({
      facility: this.selectedFacility,
      specialty: this.selectedSpecialty
    });
  }
}
