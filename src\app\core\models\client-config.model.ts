export interface Props {
  payload: string | boolean;
  type: string;
}
export interface ClientConfigRaw {
  clientId: string;
  props: Props[];
  token: string;
}
export interface ClientConfigParsed {
  clientId: string;
  props: { [key: string]: string | boolean };
  token: string;
}
export interface ClientConfig {
    id: string;         
    code: number;       
    vendorName?: string;
    language?:string
    env: string;
    darkMode?:boolean;
    menuItems?: MenuItem[]; 
  }
  export interface MenuItem {
    menuItemId: number;
    menuItemName: string;
    description:string;
    channel:string;
  }
