import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DateFormatterService {

  constructor() { }

  /**
   * Convert date from "30/1" format to "30 Jan 2025"
   */
  formatDate(dateStr: string): string {
    if (!dateStr || !dateStr.includes('/')) {
      return dateStr; // Return as-is if not in expected format
    }

    const [day, month] = dateStr.split('/');
    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    const monthIndex = parseInt(month) - 1;
    const monthName = monthNames[monthIndex] || month;
    const currentYear = new Date().getFullYear();

    return `${day} ${monthName} ${currentYear}`;
  }

  /**
   * Get day of week from date string "30/1"
   */
  getDayOfWeek(dateStr: string): string {
    if (!dateStr || !dateStr.includes('/')) {
      return 'Tuesday'; // Default fallback
    }

    const [day, month] = dateStr.split('/');
    const currentYear = new Date().getFullYear();
    
    // Create date object (month is 0-indexed in JavaScript Date)
    const date = new Date(currentYear, parseInt(month) - 1, parseInt(day));
    
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return dayNames[date.getDay()];
  }

  /**
   * Format full date and time string
   * Convert "30/1" and "6:30 PM" to "Tuesday 30 Jan 2025, 6:30 PM"
   */
  formatDateTime(dateStr: string, timeStr: string): string {
    if (!dateStr || !timeStr) {
      return 'Date and time not selected';
    }

    const formattedDate = this.formatDate(dateStr);
    const dayOfWeek = this.getDayOfWeek(dateStr);
    
    return `${dayOfWeek} ${formattedDate}, ${timeStr}`;
  }
}
