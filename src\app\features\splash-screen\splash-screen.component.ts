import { HttpErrorResponse } from '@angular/common/http';
import {
  ChangeDetectorRef,
  Component,
  Inject,
  NgZone,
  OnInit,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { HelperService } from 'src/app/core/helpers/helper.service';
import { PlatformDetectionService } from 'src/app/core/helpers/platform-detection.service';
import { ShimmerEffectService } from 'src/app/core/helpers/shimmer-effect.service';
import { ToastService } from 'src/app/core/helpers/toast.service';
import { AuthService } from 'src/app/core/services/auth.service';
import { BridgeService } from 'src/app/core/helpers/bridge.service';
import { NotificationService } from 'src/app/core/services/notification.service';
import { DOCUMENT } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ClientConfigService } from 'src/app/core/helpers/client-config.service';
import { Language } from 'src/app/core/enums/client-config.enum';
import { CommonModule } from '@angular/common';
import { DarkModeService } from 'src/app/core/helpers/dark-mode.service';
import { ClientConfigRaw } from 'src/app/core/models/client-config.model';

@Component({
  selector: 'app-splash-screen',
  templateUrl: './splash-screen.component.html',
  styleUrls: ['./splash-screen.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule,    TranslateModule  ],
})
export class SplashScreenComponent implements OnInit {
  isInitialized: boolean = false;
  client: any;
  darkMode:any
  constructor(
    private headerBreadcrumb: HelperService,
    private auth: AuthService,
    private notificationService: NotificationService,
    private shimmer: ShimmerEffectService,
    private cdr: ChangeDetectorRef,
    private bridge: BridgeService,
    private toast: ToastService,
    private router: Router,
    private ngZone: NgZone,
    private platformservice: PlatformDetectionService,
    private translate: TranslateService,
    private helperService: HelperService,
    private clientConfigService: ClientConfigService,
    private ActiveRoute: ActivatedRoute,
    private darkModeService: DarkModeService,

    @Inject(DOCUMENT) private document: Document
  ) {
    this.headerBreadcrumb?.header?.next({
      name: '',
      isDashboard: false,
      showFilter: false,
      showHeader: false,
    });
  }

  ngOnInit() {
    this.client = this.clientConfigService.getClientCodeSnapshot();
    console.log('Client Code (available immediately):', this.client);
    this.switchLanguage('en');
    if (this.platformservice?.getPlatform() === 'Unknown') {
      this.router?.navigate(['/dashboard']);
    } else {
      this.darkMode = this.clientConfigService.getClientdarkModeSnapshot() 
console.log('this.darkMode',this.darkMode);
//alert(`this.darkMode->>> ${this.darkMode}`)
      this.bridge?.onInitialize?.subscribe((data: any) => {
        if (!this.isInitialized) localStorage.clear();
        console.log(data);

        const parsedData = this.clientConfigService.loadConfig(data);
        console.log('parsed--->', parsedData);

        localStorage?.setItem('clientId', parsedData.clientId);

        // console.log(data);
        // alert(JSON.stringify(parsedData));
        this.authenticateUser(
          parsedData.token,
          parsedData.clientId,
          parsedData
        );
      });
    }
  }

  authenticateUser(token: string, clientId: any, data: any) {
    const payload = {
      token,
    };
    this.auth?.authentication(payload)?.subscribe({
      next: (res: any) => {
        localStorage?.setItem('accessToken', res?.data?.access_token);
        localStorage?.setItem('refreshToken', res?.data?.refresh_token);
        this.loadClientConfig(clientId, data);

        localStorage.setItem('loggedin', '1');

        this.isInitialized = true;
      },
      error: (error: HttpErrorResponse) => {
        this.toast?.presentToast(error?.error?.message);
      },
    });
  }
  private loadClientConfig(clientId: any, data: any) {
    console.log(clientId);

    this.clientConfigService.setClientId(clientId).subscribe({
      next: config => {
        console.log('✅ Client config loaded after auth:', config);
        this.client = config.code;

        this.clientConfigService.updateConfig({
          darkMode: data?.props.darkMode ? data?.props.darkMode : false,
          language:
            data?.props.language === 'ar' ? Language.Arabic : Language.English,
        });

        this.switchLanguage(
          data?.props.language === 'ar' ? Language.Arabic : Language.English
        );
        this.helperService.setChannelValue(
          this.clientConfigService.getClientSnapshot() ?? 'unknown'
        );
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
        console.log(prefersDark);

        // Initialize dark mode based on preference
        this.darkModeService.toggleDarkMode(
          this.clientConfigService.getClientSnapshot(),
          this.clientConfigService.getClientdarkModeSnapshot()
        );
        this.getDashboardDetails();
      },
      error: err => {
        console.error('❌ Failed to load client config:', err.message);
      },
    });
  }
  switchLanguage(language: any) {
    console.log('------', language);

    this.translate.use(language);
    this.translate.setDefaultLang(language); // fallback language
    localStorage.setItem('languageSelected', language);
    if (language === 'ar') {
      this.document.documentElement.setAttribute('dir', 'rtl');
      document.body.classList.add('arabic-translation-div');
    } else {
      this.document.documentElement.setAttribute('dir', 'ltr');
      document.body.classList.remove('arabic-translation-div');
    }
  }

  getDashboardDetails() {
    this.auth?.getDashboardDetails().subscribe({
      next: (secondRes: any) => {
        localStorage?.setItem('userID', secondRes?.data?.id);

        localStorage?.setItem('userInfo', JSON.stringify(secondRes?.data));
        this.auth?.name$?.next({
          name: secondRes?.data?.fullName,
          FN: secondRes?.data?.firstName,
          LN: secondRes?.data?.lastName,
        });
//this.helperService.userExist.next(secondRes?.data.userExists)
this.helperService.setUserExists(secondRes?.data.userExists)

        this.router.navigate(['dashboard']);
      },
      error: (secondError: HttpErrorResponse) => {
        console.error('Second API error:', secondError?.message);
        this.toast?.presentToast(secondError?.error?.message);
      },
      complete: () => this.shimmer?.hide(), // Hide spinner after both calls
    });
  }
}
// function takeUntilDestroyed(): import("rxjs").OperatorFunction<any, unknown> {
//   throw new Error('Function not implemented.');
// }

