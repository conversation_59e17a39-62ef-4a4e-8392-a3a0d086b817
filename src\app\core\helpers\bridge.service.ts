import { Injectable, Ng<PERSON><PERSON> } from '@angular/core';
import { Subject } from 'rxjs';
import { AudioPlayer } from '../enums/bridge.enum';
import { ClientConfigService } from './client-config.service';

@Injectable({
  providedIn: 'root',
})
export class BridgeService {
  orderID: any;
  paymentMethod: any;
  onInitialize = new Subject<any>();
  nativePaymentListener = new Subject<any>();
  nativeTalkingMedListener = new Subject<any>();

  constructor(private ngZone: NgZone,private clientConfigService:ClientConfigService) {}

  /** from native to webview **/
  private initializeWebSDK(): void {
    (window as any).initializeWebSDK = (payload: string) => {
      this.ngZone.run(() => {
        const data = JSON.parse(payload);
        console.log('initializeWebSDK ===> ', JSON.stringify(data));
        this.onInitialize.next(data);
      });
    };
  }

  /** from native to webview **/
  private updateWebSDK(): void {
    (window as any).updateWebSDK = (payload: string) => {
      this.ngZone.run(() => {
        const data = JSON.parse(payload);
        console.log('updateWebSDK ====> ', JSON.stringify(data));
      });
    };
  }

  /** from webview to native **/
  listenerWebSDK(payload: any): void {
    this.ngZone.runOutsideAngular(() => {
      if (!!(window as any).ANDROID?.listenerWebSDK) {
        const data = JSON.stringify(payload);
        (window as any).ANDROID.listenerWebSDK(data);
      }
      if (!!(window as any).webkit?.messageHandlers?.IOS?.postMessage) {
        const data = JSON.stringify({
          function: 'listenerWebSDK',
          data: payload,
        });
        (window as any).webkit.messageHandlers.IOS.postMessage(data);
      }
    });
  }

  /** from webview to native **/
  startPaymentWebSDK(
    orderVerificationData: any,
    orderDetails: any,
    config:any,
    paymentMethod?: number,
  ): void {
    const userInfo =
      JSON.parse(localStorage.getItem('userInfo') || 'null') || null;
    const payload = {
      orderId: orderDetails?.orderId.toString(),
      warehouseId: orderVerificationData?.warehouseId.toString(),
      warehouseName: orderVerificationData?.warehouseName,
      phoneNumber: userInfo?.mobile,
      patientMrn: orderVerificationData?.MRN.toString(),
      amount: orderDetails?.subTotal.toString(),
      email: userInfo?.email,
      fullName: userInfo?.fullName,
      paymentMethod: paymentMethod,
      prescriptionSubType: orderDetails?.prescriptionSubType.toString(),
      config:orderDetails?.prescriptionSubTyp === 3 ? config?.payment?.dubai :config?.payment?.cerner,
      origin:this.clientConfigService.getClientSnapshot()
    };
    this.orderID = payload.orderId;
    this.paymentMethod = payload.paymentMethod;
    this.ngZone.runOutsideAngular(() => {
      if (!!(window as any).ANDROID?.startPaymentWebSDK) {
        const data = JSON.stringify(payload);
        (window as any).ANDROID.startPaymentWebSDK(data);
      }
      if (!!(window as any).webkit?.messageHandlers?.IOS?.postMessage) {
        const data = JSON.stringify({
          function: 'startPaymentWebSDK',
          data: payload,
        });
        (window as any).webkit.messageHandlers.IOS.postMessage(data);
      }
    });
  }

  /** from native to webview **/
  private paymentListenerWebSDK(): void {
    (window as any).paymentListenerWebSDK = (payload: any) => {
      this.ngZone.run(() => {
        console.log('paymentListener initial ===> ', payload);
        console.log(
          'paymentListener stringified ===> ',
          JSON.stringify(payload)
        );
        const data = JSON.parse(payload);
        console.log('paymentListener parsed ===> ', payload);
        this.nativePaymentListener.next(data);
      });
    };
  }

  /** from native to webview **/
  private audioPlayerListenerWebSDK(): void {
    (window as any).audioPlayerListenerWebSDK = (payload: any) => {
      this.ngZone.run(() => {
        console.log('audioPlayerListenerWebSDK initial ===> ', payload);
        console.log(
          'audioPlayerListenerWebSDK stringified ===> ',
          JSON.stringify(payload)
        );
        const data = JSON.parse(payload);
        console.log('audioPlayerListenerWebSDK parsed ===> ', data);
        this.nativeTalkingMedListener.next(data);
      });
    };
  }

  /** from webview to native **/
  openLeafletWebSDK(url?: any): void {
    this.ngZone.runOutsideAngular(() => {
      if (!!(window as any).ANDROID?.openLeafletWebSDK) {
        const data = JSON.stringify(url);
        (window as any).ANDROID.openLeafletWebSDK(data);
      }
      if (!!(window as any).webkit?.messageHandlers?.IOS?.postMessage) {
        const data = JSON.stringify({
          function: 'openLeafletWebSDK',
          data: url,
        });
        (window as any).webkit.messageHandlers.IOS.postMessage(data);
      }
    });
  }
  /** from webview to native **/
  exitWebview(): void {
    this.ngZone.runOutsideAngular(() => {
      if (!!(window as any).ANDROID?.exitWebview) {
        console.log('exitWebview ANDROID ===> ', true);
        (window as any).ANDROID.exitWebview();
      }
      if (!!(window as any).webkit?.messageHandlers?.IOS?.postMessage) {
        const data = JSON.stringify({
          function: 'exitWebview',
        });
        console.log('exitWebview IOS ===> ', true);
        (window as any).webkit.messageHandlers.IOS.postMessage(data);
      }
    });
  }
  /** from webview to native **/
  triggerAudioPlayerWebSDK(text?: any, state?: any): void {
    const start = {
      type: AudioPlayer.Start,
      data: {
        text: text,
      },
    };
    const stop = {
      type: AudioPlayer.Stop,
    };
    this.ngZone.runOutsideAngular(() => {
      if (!!(window as any).ANDROID?.triggerAudioPlayerWebSDK) {
        const data =
          state === '1' ? JSON.stringify(start) : JSON.stringify(stop);
        (window as any).ANDROID.triggerAudioPlayerWebSDK(data);
      }
      if (!!(window as any).webkit?.messageHandlers?.IOS?.postMessage) {
        const data = JSON.stringify({
          function: 'triggerAudioPlayerWebSDK',
          data: state === '1' ? start : stop,
        });
        (window as any).webkit.messageHandlers.IOS.postMessage(data);
      }
    });
  }
  themeSettingsWebSDK(shimmer:boolean): void {
    const payload={
      darkMode:this.clientConfigService.getClientdarkModeSnapshot() ?? false,
      client:this.clientConfigService.getClientSnapshot(),
      shimmer:shimmer
    }
    this.ngZone.runOutsideAngular(() => {
      if (!!(window as any).ANDROID?.themeSettingsWebSDK) {
        const data = JSON.stringify(payload);
        (window as any).ANDROID.themeSettingsWebSDK(data);
      }
      if (!!(window as any).webkit?.messageHandlers?.IOS?.postMessage) {
        const data = JSON.stringify({
          function: 'themeSettingsWebSDK',
          data: payload,
        });
        (window as any).webkit.messageHandlers.IOS.postMessage(data);
      }
    });
  }
  base64PDFWebSDK(base64:string): void {
    const payload={
      base64PDF:base64,
    }
    this.ngZone.runOutsideAngular(() => {
      if (!!(window as any).ANDROID?.base64PDFWebSDK) {
        const data = JSON.stringify(payload);
        (window as any).ANDROID.base64PDFWebSDK(data);
      }
      if (!!(window as any).webkit?.messageHandlers?.IOS?.postMessage) {
        const data = JSON.stringify({
          function: 'base64PDFWebSDK',
          data: payload,
        });
        (window as any).webkit.messageHandlers.IOS.postMessage(data);
      }
    });
  }
  
  setupListeners(): void {
    this.initializeWebSDK();
    this.updateWebSDK();
    this.paymentListenerWebSDK();
    this.audioPlayerListenerWebSDK();
  }
}
