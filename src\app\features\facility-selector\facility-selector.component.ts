import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  IonContent,
  IonButton,
  IonItem,
  IonLabel,
  ModalController
} from '@ionic/angular/standalone';

@Component({
  selector: 'app-facility-selector',
  templateUrl: './facility-selector.component.html',
  styleUrls: ['./facility-selector.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonContent,
    IonButton,
    IonItem,
    IonLabel
  ]
})
export class FacilitySelectorComponent {
  @Input() selectedFacility: string = '';

  facilities = [
    'SEHA CLINIC Corniche',
    'Another CLINIC'
  ];

  constructor(private modalController: ModalController) {}

  selectFacility(facility: string) {
    this.modalController.dismiss({
      selectedFacility: facility
    });
  }

  close() {
    this.modalController.dismiss();
  }
}
