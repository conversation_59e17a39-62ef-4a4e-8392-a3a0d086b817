import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { from, Observable, switchMap } from 'rxjs';
import { URLS } from '../constants/index';
import { PDFDocument } from 'pdf-lib';
import imageCompression from 'browser-image-compression';

@Injectable({
  providedIn: 'root',
})
export class UploadFileService {
  constructor(private httpClient: HttpClient) {}

  uploadFile(file: any): Observable<any> {
    return this.httpClient.post<any>(URLS.UPLOAD_FILE, file);
  }
  async fetchBlobData(
    blobUri: string
  ): Promise<{ base64: string; dataUrl: string }> {
    try {
      // Fetch the Blob content using HttpClient
      const blob: any = await this.httpClient
        .get(blobUri, { responseType: 'blob' })
        .toPromise();

      // Convert Blob to Base64
      const base64 = await this.blobToBase64(blob);

      // Convert Blob to Data URL
      //   const dataUrl = URL.createObjectURL(blob);

      const dataUrl = this.getDataUrlWithoutPrefix(blob);

      return { base64, dataUrl };
    } catch (error) {
      console.error('Error fetching blob data:', error);
      throw error;
    }
  }

  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result as string);
      };
      reader.onerror = error => reject(error);
      reader.readAsDataURL(blob);
    });
  }
  private getDataUrlWithoutPrefix(blob: any): string {
    const dataUrl = URL.createObjectURL(blob);
    const prefixToRemove = 'blob:';
    if (dataUrl.startsWith(prefixToRemove)) {
      return dataUrl.slice(prefixToRemove.length);
    }
    return dataUrl;
  }
  async blobToFile(blobUrl: string, filename: string): Promise<File> {
    const response = await fetch(blobUrl);
    const blob = await response.blob();
    const file = new File([blob], filename, { type: blob.type });
    console.log(file);

    return file;
  }

  async compressImage(file: any) {
    return await imageCompression(file, {
      maxSizeMB: 1,
      maxWidthOrHeight: 1920,
      useWebWorker: true,
    });
  }

  extractFilenameFromBlobUrl(blobUrl: string, extension?: any): string {
    // Example Blob URL format: blob:http://localhost:8100/ba29c9cf-c765-4708-9e25-75e7e52f603e
    const match = blobUrl.match(/\/([^\/?#]+)$/);
    if (match && match.length > 1) {
      return match[1] + (extension === 'pdf' ? '.pdf' : '.jpg');
    } else {
      return 'unknown_filename';
    }
  }
  getImage(apiUrl: string): Observable<Blob> {
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${localStorage.getItem('accessToken')}`
    );
    return this.httpClient.get(apiUrl, { headers, responseType: 'blob' });
  }
   
  // getPuraPDF(pid: any): Observable<Blob> {
  //   return this.httpClient.get<any>(
  //     URLS.urlWithParams(URLS.GET_PURA_PRESCRIPTION_PDF, [
  //       { key: 'pid', value: '121285' },
  //     ]),{responseType: 'blob' as const}
  //   )

  //   }
  // getPuraPDF(pid: any): Observable<Blob> {
  //   return this.httpClient.get<Blob>(
  //     URLS.urlWithParams(URLS.GET_PURA_PRESCRIPTION_PDF, [
  //       { key: 'pid', value: pid },
  //     ]),
  //     {
  //       responseType: 'blob' as const, // 👈 required to match overload
  //     }
  //   );
  // }
  
  
  getPuraPDF(pid: string): Observable<Blob> {
    const url = `${URLS.GET_PURA_PRESCRIPTION_PDF}/${pid}`;
    return this.httpClient.get(url, {
      responseType: 'blob' as 'blob'
    });
  }
  
  // getPuraPDFBase64(pid: string): Observable<string> {
  //   const url = `${URLS.GET_PURA_PRESCRIPTION_PDF}/${pid}`;
  //   return this.httpClient.get(url, { responseType: 'blob' as 'blob' }).pipe(
  //     switchMap(blob => this.convertBlobToBase64(blob))
  //   );
  // }

  getPuraPDFBase64(pid: string): Observable<string> {
    const url = `${URLS.GET_PURA_PRESCRIPTION_PDF}/${pid}`;
    return this.httpClient.get(url, { responseType: 'blob' }).pipe(
      switchMap(blob => from(this.compressPdfBlob(blob))), // compress first
      switchMap(compressedBlob => this.convertBlobToBase64(compressedBlob))
    );
  }
  private async compressPdfBlob(blob: Blob): Promise<Blob> {
    const pdfBytes = await blob.arrayBuffer();
    const pdfDoc = await PDFDocument.load(pdfBytes);
  
    const newPdf = await PDFDocument.create();
    const pages = await newPdf.copyPages(pdfDoc, pdfDoc.getPageIndices());
  
    pages.forEach(page => newPdf.addPage(page)); // Copy pages (no metadata, no attachments)
  
    const compressedPdfBytes = await newPdf.save(); // You can enable compression here
    return new Blob([compressedPdfBytes], { type: 'application/pdf' });
  }

  private convertBlobToBase64(blob: Blob): Observable<string> {
    return new Observable<string>((observer) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = (reader.result as string).split(',')[1]; // strip "data:application/pdf;base64,"
        observer.next(base64);
        observer.complete();
      };
      reader.onerror = (error) => observer.error(error);
      reader.readAsDataURL(blob);
    });
  }


  
  
}
