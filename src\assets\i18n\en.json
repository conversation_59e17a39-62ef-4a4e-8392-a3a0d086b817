{"splash": {"please-wait": "Starting up, please wait!"}, "address": {"title": "Add Address", "add-address-button": "Add Address Details"}, "address-patient": {"title": "Add Address", "add-address-button": "Add Address Details"}, "location-direction": {"distance": "Remaining Distance", "duration": "Remaining Duration"}, "dashboard": {"main-title": "Welcome to VCP", "title-order-medication": "Order Medication Delivery", "content-delivery-order": "Create a new delivery order.", "title-my-order": "My Orders", "content-track-order": "View and track your orders.", "title-seha-prescription-DAMAN": "SEHA Prescriptions", "title-seha-prescription-PURA": "Prescriptions", "content-medication-delivary": "View and order for medication delivery.", "title-login": "<PERSON><PERSON>"}, "upload-prescription": {"main-title": "Delivery Address", "add-address-button": "Add Delivery Address", "title-prescription": "Prescription Details", "title-facility": "Prescription Medical Facility", "error-insurance": "Please Select Insurance", "error-insurance-card": "Error. Kindly upload readable pictures of your insurance card back.", "non-seha-message": "JPG/PNG/GIF/PDF files up to 2MB size", "non-seha-image": "Please Upload the Image", "non-seha-insurance-card": "Error. Kindly upload readable pictures of your insurance card back.", "erx-valid": "Entered ERX# is not valid", "erx-expired": "Entered ERX# is expired", "or": "OR", "error-message-non": "Error. Kindly upload readable pictures of your prescription.", "uplode-file": "You cannot upload more than 3 prescription file / images", "access-your-seha": "You can access your SEHA prescription by clicking on ‘View Prescription’", "subtitle": "Additional Notes", "checkbox-consent": "I acknowledge that the prescription is uploaded with my consent and is subject to insurance approval and availability of brand and quantity.", "alert-DOH-guidelines": "As per DOH guidelines, we do not deliver controlled and semi-controlled medication.", "alert-DHA-guidelines": "As per DHA guidelines, we do not deliver controlled and semi-controlled medication.", "view-prescription-button": "View Prescription", "proceed-button": "Proceed"}, "notifications": {"main-title": "Recent", "title": "Earlier"}, "my-order": {"action-required-tab": "Action Required", "in-progress-tab": "In-progress", "history": "History"}, "order-detail": {"summary-button": "Summary", "medication-details-button": "Medication Details", "message-payment-pending": "Your prescription is pending for payment.", "title-insurance": "Insurance not approved", "title-insurance-approved": "Insurance Approved with Co-Payment", "insurance-approved": "Your Insurance is Approved!", "title-message": "Your order is being reviewed by our pharmacist", "title-under-processing": "Your order is under processing", "title-out-for-delivery": "Your Medication is Out for Delivery", "title-delivery-failed": "Delivery Failed", "title-insurance-inprogress": "Your insurance is inprogress", "title-insurance-approval": "Your insurance is under approval", "title-re-submit": "Re-Submit Your Order", "title-rejected": "Prescription Rejected", "title-delivery-Successful": "Delivery Successful", "title-order-cancel": "Order Cancelled", "ion-chip-refill": "Refill", "ion-chip-Prescription": "Prescription #", "ion-chip-order": "Order #", "EID": "EID #", "prescription-date": "Prescription Date", "prescriber": "Prescriber", "location": "Ordering Location", "AED": "AED", "qty": "Qty", "note-message": "Removing approved medicines by insurance will need to send for insurance approval again", "modify-order-button": "Modify Order", "proceed-button": "Proceed", "resend-for-approval-button": "Resend for Approval", "edit-prescription-button": "Edit Prescription", "track-order-button": "Track Order", "cancel-order": "Cancel Order"}, "order-tabs": {"action-required": {"title-prescription": "Prescription #", "text-Order": "Order #", "span-items": "Items", "span-item": "<PERSON><PERSON>", "AED": "AED", "no-orders": "No Orders", "no-action-required": "No action required"}, "history": {"title-prescription": "Prescription #", "text-Order": "Order #", "span-items": "Items", "span-item": "<PERSON><PERSON>", "AED": "AED", "no-orders": "No Orders", "no-action-required": "No action required", "tab-medication-details": {"text-content": "Play All your Prescriptions", "card-prescription": "Prescription#", "EID": "EID#", "chip-tab": "Tab", "pcs": "Pcs", "item-dosage": "Dosage Instructions", "span-play": "Play", "span-stop": "Stop", "span-view": "View Leaflet"}, "tab-summary": {"main-title": "Order#", "chip-refill": "Refill", "chip-prescription": "Prescription #", "text-date": "Prescription Date", "text-prescriber": "Prescriber", "order-location": "Ordering Location", "sub-title": "Prescriptions", "tab": "Tab", "AED": "AED", "psc": "Psc", "list-title-total": "Items Total", "list-title-shipping": "Shipping Fee", "list-content": "Free", "list-title": "Total", "title-rating": "Review & Ratings", "reschedule-delivery-button": "Reschedule Delivery", "rate-button": "Rate & Review"}}, "inprogress": {"title-prescription": "Prescription #", "text-Order": "Order #", "span-items": "Items", "span-item": "<PERSON><PERSON>", "AED": "AED", "no-orders": "No Orders", "no-action-required": "No action required"}}, "track-order": {"main-title": "Order Details", "text-date": "Delivery Date/Time", "item-order": "Order #", "item-status": "Order Status", "track-your-package-button": "Track your Package"}, "complete-profile": {"main-title": "Complete your profile", "text-message": "We need a few details to complete your profile to proceed with your medication deliver", "complete-profile-button": "complete profile"}, "otp-verification": {"main-title": "Enter OTP", "sub-title": "Enter OTP sent on your Mobile# +712*****77473.", "resend-button": "Resend", "verify-button": "Verify"}, "patient-details": {"order-title": "Order Medication Delivery", "order-content": "Create a new delivery order.", "brother": "Brother", "MRN": "MRN #", "date-of-birth": "Date of Birth", "list-content": "12 June 2024", "age": "Age", "contact": "Contact #", "nationality": "Nationality", "pakistani": "Pakistani", "gender": "Gender", "male": "Male", "marital-status": "Marital Status", "married": "Married", "language": "Language", "english/urdu": "English/Urdu", "proceed-button": "Proceed"}, "patient-registration": {"main-title": "Register Patient/Dependents", "text-address": "Address*", "error-message": "JPG/PNG/GIF/PDF files up to 2MB size", "confirm-button": "Confirm"}, "patient-success": {"main-title": "Patient Added Successfully!", "title-message": "You’ve successfully registered your Patient with VCP.", "next-button": "NEXT"}, "payment-journey": {"main-title": "Shipping Address", "title-delivery-time": "Delivery Time", "text-message": "For immediate delivery, we've picked the earliest Date/Time slot for you.", "title-message": "Your order will be delivered in next 24-48 hours. Our rider will contact you once your order is ready for delivery.", "title-payment-options": "Payment Options", "title-payment-on-delivery": "Payment on Delivery", "title-card": "Credit/Debit Card", "total-due": "Total Due", "AED": "AED", "place-order": "Place Order", "reschedule-order": "Reschedule Order"}, "prescription-details": {"main-title": "Active Prescriptions", "title-ready": "Your Medicines are Ready!", "title-delivered": "This Prescription was delivered", "title-lapsed": "This Prescription has been Lapsed", "new": "New Prescription", "refill": "Refill", "prescription": "Prescription #", "EID": "EID", "prescription-date": "Prescription Date", "prescriber": "Prescriber", "location": "Ordering Location", "title-order": "Prescription available for order", "note-text": "Collect this medicine from the pharmacy. It will be discontinued soon", "text-message": "Medicine is about to be discontinued within 72 hours", "title-order-not-allowed": "Order not Allowed", "title-already-ordered": "Prescription already ordered", "title-doctor-check": "Your medication was accepted through Doctor Check", "title-kiosk-check": "Your medication was accepted through kiosk / Seha", "place-order-button": "Place Order", "view-orders-button": "View Orders", "download-prescription": "Download Prescription"}, "seha-order-listing": {"title-prescription": "Prescription #", "text-Order": "Order #", "span-items": "Items", "AED": "AED"}, "seha-prescription": {"main-title": "Active Prescriptions", "title-ready": "Your Medicines are Ready!", "title-delivered": "This Prescription was delivered", "title-lapsed": "This Prescription has been Lapsed", "new": "New Prescription", "refill": "Refill", "prescription": "Prescription #", "EID": "EID #", "prescription-date": "Prescription Date", "prescriber": "Prescriber", "location": "Ordering Location", "title-more-medicine": "more medicine", "text-message": "Your prescription is about to expire after 48 hours. Kindly book online consultation through PURA.", "title-doctor-check": "Your medication was accepted through Doctor Check", "deliver-medication-button": "Deliver Medication", "view-orders": "View Orders", "container": "No Seha Prescription Found"}, "order-cancel": {"main-title": "Order has been cancelled Successfully", "title": "Get your medicines delivered by placing a new order.", "go-to-home-button": "Go to Home"}, "order-success": {"main-title-start": "Your Prescription# ", "main-title-end": "has been uploaded successfully.", "main-title-sp": "Order has been Placed Successfully with Order#", "title": "You will get a notification on order status once your prescription is verified", "title-sp": "Your Order is under process, and you will receive order update soon.", "track-order-button": "Track Order", "go-home-button": "Go Home"}, "reschedule-successfully": {"main-title": "Order rescheduled successfully with Order#", "title": "You will get a notification once your package is ready to be shipped.", "track-order-button": "Track Order", "go-to-home-button": "Go to Home"}, "payment-by-card": {"main-title": "Payment Successful!", "sub-title": "Payment has been received successfully with", "span-order": "Order#", "span-transaction": "Transaction #", "span-amount": "Amount", "AED": "AED", "span-payment-type": "Payment Type", "track-order-button": "Track Order", "go-home-button": "Go Home", "error-message": "Payment Failed!", "message": "There has been some error in transferring funds.", "try-again-button": "Try Again"}, "payment-on-delivery": {"main-title": "Order has been placed successfully with Order#", "title": "Your order is under process, and you will receive order updates soon.", "track-order-button": "Track Order", "go-home-button": "Go Home"}, "bottom-sheet-modal-address-confirmation": {"main-title": "Where do you want to deliver these medicines?", "title": "Kindly confirm your delivery address!", "delivery-address": "Add new delivery address", "confirm-address-button": "Confirm Address"}, "bottom-sheet-modal-feedback": {"title": "Rate and Review", "title-experience": "How was your Order", "title-message": "Only 150 characters are allowed.", "note-message": "Only English characters are allowed.", "submit-button": "submit"}, "bottom-sheet-modal-for-addresses": {"title": "Enter address details", "save-changes-button": "Save Changes"}, "bottom-sheet-modal-order-cancel": {"title": "Order Cancellation Reasons", "submit-button": "Submit"}, "bottom-sheet-modal-with-filtration": {"title": "Apply Filter", "sub-title": "Select generated date range", "status": "Status", "prescriber": "Prescriber", "prescriber-status": "Prescriber Status", "apply-button": "Apply"}, "bottom-sheet-modal-with-selection": {"main-title": "Prescription Medical Facility", "patients": "Patients", "title-insurance-types": "Insurance Types", "select-button": "Select"}, "layout": {"header": {"title": "Welcome,"}}, "breadcrumb": {"upload-prescription": "Upload Prescription", "my-orders": "My Orders", "dashboard": "dashboard", "add-address": "Add Address", "track-order": "Track Order", "notifications": "Notifications", "order-details": "Order Details", "patient-details": "Patient Details", "register-patients": "Register Patients", "delivery-details": "Delivery Details", "payment": "Payment", "prescription-details": "Prescription Details", "view-orders": "View Orders", "seha-prescription": "Seha Prescription"}, "alert": {"alert": "<PERSON><PERSON>", "go-back-button": "go back", "cancel-my-order-button": "Cancel my Order", "order-detail-message": "If you cancel this order you will have to initiate this process from the start", "pura-button": "Book appointment in PURA", "cancel-button": "Cancel", "close-button": "Close", "status-expired": "Prescription cannot be delivered as it is expired. Download the PURA app for online doctor consultation and get the new prescription delivered asap.", "status-controlled": "As per DOH guidelines, we do not deliver controlled and semi-controlled medication.Please collect your medication from the nearest OP Pharmacy within 72 hours from the prescription Date/Time", "status-lapsed": "This medication has been discontinued and cannot be ordered. You need to book an appointment with the doctor for a new prescription", "status-warning": "Home Delivery Order Cannot be placed 48 hours before the medicine stop date", "status-delivary": "ORDER MEDICATION DELIVERY", "Create-patient": "CREATE PATIENT", "create-user": "Please Create User First", "order-available": "There are no Order Available", "fence-out": "<PERSON><PERSON> Out", "status-outside": "This location is outside UAE.", "ok": "ok"}, "placeholder": {"search-location": "Search Location", "select": "Select", "select-medical-facility": "Select Medical Facility", "enter-erx-number": "Enter ERX Number", "Notes-to-Pharmacist": "Notes to Pharmacist for required medicines, quantity and preferred brand", "first-name": "Enter First Name", "last-name": "Enter Last Name", "middle-name": "Enter Middle Name", "mobile-number": "Enter Mobile Number", "email": "<PERSON><PERSON>", "text": "Enter text", "custom-reason": "Write Custom Reason..."}, "label": {"available-client": "Available Clients", "available-modes": "Available Modes", "available-language": "Available Language", "available-personas": "Available Personas", "select-insurance": "Select Insurance", "insurance-card-front": "Insurance Card Front *", "insurance-card-back": "Insurance Card Back *", "erx-number": "ERX Number", "first-name": "First Name*", "last-name": "Last Name*", "middle-name": "Middle Name", "date-of-birth": "Date Of Birth*", "mobile-number": "Mobile Number*", "email": "Email*", "gender": "Gender*", "marital-status": "Marital Status*", "emirates-expiry": "Emirates ID Expiry*", "emirates-document-front": "Emirates ID Document Front*", "emirates-document-back": "Emirates ID Document Back*", "available-dates": "Available Dates", "available-time-slots": "Available Time Slots", "address-line1": "Address Line 1", "address-line2": "Address Line 2 (Optional)", "city": "City", "enter-number": "Enter prescription number", "enter-order-number": "Enter order number", "write-review": "Write a review to help others know what’s good"}}