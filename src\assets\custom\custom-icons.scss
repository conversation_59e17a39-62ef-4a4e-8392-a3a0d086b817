@font-face {
  font-family: 'das';
  src:
    url('../fonts/das.ttf?lyp2q3') format('truetype'),
    url('../fonts/das.woff?lyp2q3') format('woff'),
    url('../fonts/das.svg?lyp2q3#das') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^='das-'],
[class*=' das-'] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'das' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.das-bell-pura:before {
  content: "\e93c";
}
.das-headset_mic:before {
  content: "\e93d";
}
.das-download:before {
  content: "\e93b";
}
.das-select-down-arrow:before {
  content: "\e93a";
}
.das-serach-new:before {
  content: "\e938";
}
.das-file-Icon-new:before {
  content: "\e939";
}
.das-filter-new:before {
  content: "\e92d";
}
.das-payment-on-delivery:before {
  content: "\e92e";
}
.das-samsung-pay:before {
  content: "\e92f";
}
.das-credit-card:before {
  content: "\e930";
}
.das-warning:before {
  content: "\e931";
}
.das-file-Icon:before {
  content: "\e932";
}
.das-edit_square:before {
  content: "\e933";
}
.das-Location:before {
  content: "\e934";
}
.das-select-arrow:before {
  content: "\e935";
}
.das-person:before {
  content: "\e936";
}
.das-header-left:before {
  content: "\e937";
}
.das-carbon_task-complete:before {
  content: "\e92a";
}
.das-pill:before {
  content: "\e92b";
}
.das-Upload-Prescription:before {
  content: "\e92c";
}
.das-close:before {
  content: '\e929';
}
.das-notification:before {
  content: '\e927';
}
.das-off:before {
  content: '\e928';
}
.das-Location-Tick:before {
  content: '\e925';
}
.das-Watch-icon:before {
  content: '\e926';
}
.das-refresh:before {
  content: '\e91e';
}
.das-Language:before {
  content: '\e91f';
}
.das-Marital:before {
  content: '\e920';
}
.das-Gender:before {
  content: '\e921';
}
.das-flag:before {
  content: '\e922';
}
.das-call:before {
  content: '\e923';
}
.das-mrn-icon:before {
  content: '\e924';
}
.das-Search-icon:before {
  content: '\e91d';
}
.das-bell-icon:before {
  content: '\e918';
}
.das-add-square-icon:before {
  content: '\e919';
}
.das-happy-icon:before {
  content: '\e91a';
}
.das-work-icon:before {
  content: '\e91b';
}
.das-home-icon:before {
  content: '\e91c';
}
.das-shop:before {
  content: '\e917';
}
.das-tick:before {
  content: '\e916';
}
.das-filter:before {
  content: '\e914';
}
.das-Prescriber:before {
  content: '\e915';
}
.das-Add:before {
  content: '\e900';
}
.das-Attachment:before {
  content: '\e901';
}
.das-Calender-dashed:before {
  content: '\e902';
}
.das-calender-dotted:before {
  content: '\e903';
}
.das-Delete:before {
  content: '\e904';
}
.das-Edit-Square:before {
  content: '\e905';
}
.das-filter-icon:before {
  content: '\e906';
}
.das-icon-chevron-down:before {
  content: '\e907';
}
.das-icon-eye:before {
  content: '\e908';
}
.das-left-arrow:before {
  content: '\e909';
}
.das-location-pin:before {
  content: '\e90a';
}
.das-mage_tablet:before {
  content: '\e90b';
}
.das-Next:before {
  content: '\e90c';
}
.das-Play:before {
  content: '\e90d';
}
.das-Previous:before {
  content: '\e90e';
}
.das-Profile-icon:before {
  content: '\e90f';
}
.das-promo-icon:before {
  content: '\e910';
}
.das-Repeate:before {
  content: '\e911';
}
.das-Right-arrow:before {
  content: '\e912';
}
.das-Shuffle:before {
  content: '\e913';
}
