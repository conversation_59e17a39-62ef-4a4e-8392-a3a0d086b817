import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root',
})
export class PlatformDetectionService {
  constructor(private platform: Platform) {}

  isIOS(): boolean {
    return this.platform.is('ios');
  }

  isAndroid(): boolean {
    return this.platform.is('android');
  }
  // isWeb(): boolean {
  //   return this.platform.is('browser');
  // }

  getPlatform(): string {
    if (this.isIOS()) {
      return 'iOS';
    } else if (this.isAndroid()) {
      return 'Android';
    } else {
      return 'Unknown';
    }
  }
}
