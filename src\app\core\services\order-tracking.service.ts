import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { URLS } from '../constants/index';

@Injectable({
  providedIn: 'root',
})
export class OrderTrackingService {
 
  constructor(private httpClient: HttpClient) {}

 
  TrackOrder(encounterId: any): Observable<any> {
    return this.httpClient.post<any>(URLS.TRACK_ORDER,encounterId );
  }
 
}
