<ion-header class="doctor-details-header">
  <ion-toolbar class="doctor-details-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()">
        <ion-icon name="chevron-back" color="light"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="doctor-details-title">Doctor</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="doctor-details-content">
  <div class="doctor-details-container">
    <!-- Doctor Profile Section -->
    <div class="doctor-profile-section">
      <div class="doctor-avatar-large">
        <img src="assets/images/doctor-image.jpg" [alt]="doctor.name">
      </div>
      <div class="doctor-info">
        <h2 class="doctor-name">{{ doctor.name }}</h2>
        <p class="doctor-specialty">{{ doctor.specialty }}</p>
        <div class="doctor-meta">
          <div class="doctor-rating">
            <ion-icon name="star" class="star-icon"></ion-icon>
            <span class="rating-value">{{ doctor.rating }}</span>
            <span class="rating-count">({{ doctor.ratingCount }})</span>
          </div>
          <div class="doctor-languages">
            <ion-icon name="language" class="language-icon"></ion-icon>
            <span>{{ doctor.languages }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Doctor Description -->
    <div class="doctor-description">
      A fine doctor with over 12 years of experience in many countries around the world and multiple certifications under his belt.
    </div>

    <!-- Appointment Type Tabs -->
    <div class="appointment-tabs">
      <ion-segment [(ngModel)]="selectedTab" class="custom-segment">
        <ion-segment-button value="online" class="segment-button">
          <ion-label>Online</ion-label>
        </ion-segment-button>
        <ion-segment-button value="in-person" class="segment-button">
          <ion-label>In-Person</ion-label>
        </ion-segment-button>
      </ion-segment>
    </div>

    <!-- Date Selection -->
    <div class="date-selection">
      <div class="date-scroll">
        <div *ngFor="let date of availableDates"
             class="date-item"
             [class.selected]="selectedDate === date.value"
             (click)="selectDate(date.value)">
          <div class="day">{{ date.day }}</div>
          <div class="date">{{ date.date }}</div>
        </div>
      </div>
    </div>

    <!-- Location Info (only show for in-person appointments) -->
    <div class="location-info" *ngIf="selectedTab === 'in-person'">
      <div class="location-name">SEHA CLINIC Corniche</div>
      <div class="location-distance">
        <ion-icon name="location" class="location-icon"></ion-icon>
        <span>12 km away</span>
      </div>
    </div>

    <!-- Time Slots -->
    <div class="time-slots-section">
      <div class="time-period">
        <div class="period-title">Afternoon</div>
        <div class="time-slots">
          <div *ngFor="let slot of afternoonSlots"
               class="time-slot"
               [class.selected]="selectedTime === slot.time"
               [class.disabled]="slot.disabled"
               (click)="selectTime(slot.time)">
            {{ slot.time }}
          </div>
        </div>
      </div>

      <div class="time-period">
        <div class="period-title">Evening</div>
        <div class="time-slots">
          <div *ngFor="let slot of eveningSlots"
               class="time-slot"
               [class.selected]="selectedTime === slot.time"
               [class.disabled]="slot.disabled"
               (click)="selectTime(slot.time)">
            {{ slot.time }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Book Appointment Button -->
  <div class="book-button-container">
    <ion-button 
      expand="block" 
      class="book-button"
      (click)="bookAppointment()">
      Book Appointment
    </ion-button>
  </div>
</ion-content>
