import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { URLS } from '../constants/index';

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  constructor(private httpClient: HttpClient) {}
  private notificationSub: Subject<any> = new Subject();

  getNotificationRefreshReq() {
    return this.notificationSub.asObservable();
  }
  sendNotificationRefreshReq(message:any) {
    this.notificationSub.next(message);
  }
  getNotificationsList() :Observable<any>{
    return this.httpClient.get<any>(URLS.GET_NOTIFICATIONS);
  }
}
