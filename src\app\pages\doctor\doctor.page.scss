// Doctor Details Page Styles (based on doctor details modal)
.doctor-details-header {
  --background: transparent;
  --color: #ffffff;
  background: transparent;
  margin-top: 28px;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
  position: relative; 

  // Black overlay from top of modal to bottom of toolbar
  &::before {
    content: '';
    position: absolute;
    top: -28px; // Extend up to cover the margin-top area
    left: 0;
    right: 0;
    bottom: 0; // This will go to the bottom of the header including toolbar
    background: rgba(0, 0, 0, 0.04);
    z-index: 0;
  }

  .doctor-details-toolbar {
    --background: transparent;
    --color: #ffffff;
    --border-width: 0;
    --border-style: solid;
    --border-color: #16425d;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --padding-start: 12px;
    --padding-end: 16px;
    background: transparent;
    min-height: 56px;
    position: relative;
    z-index: 1;

    .doctor-details-title {
      --color: #ffffff;
      font-size: 21px;
      font-weight: 500;
      padding: 0 0 0 0;
      margin-bottom: -6px;
    }

    ion-button {
      --color: #ffffff;
      --background: transparent;

      ion-icon {
        font-size: 24px;
      }
    }
  }
}

.doctor-details-content {
  --background: transparent;
  --color: #ffffff;
  background: transparent;

  .doctor-details-container {
    padding: 24px 16px 100px 16px; // Extra bottom padding for button

    // Doctor Profile Section
    .doctor-profile-section {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;
      padding-bottom: 0px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.0);

      .doctor-avatar-large {
        width: 64px;
        height: 64px;
        border-radius: 32px;
        overflow: hidden;
        margin-right: 16px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .doctor-info {
        flex: 1;

        .doctor-name {
          color: #ffffff;
          font-size: 18px;
          font-weight: 500;
          margin: 0 0 4px 0;
        }

        .doctor-specialty {
          color: rgba(255, 255, 255, 1);
          font-size: 16px;
          font-weight: 400;
          margin: 0 0 12px 0;
        }

        .doctor-meta {
          display: flex;
          align-items: center;
          gap: 12px;

          .doctor-rating {
            display: flex;
            align-items: center;

            .star-icon {
              color: #FFA800;
              font-size: 12px;
              margin-right: 4px;
              margin-top: -5px;
            }

            .rating-value {
              color: #ffffff;
              font-size: 12px;
              font-weight: 400;
              margin-right: 4px;
            }

            .rating-count {
              color: rgba(255, 255, 255, 0.6);
              font-size: 12px;
              font-weight: 400;
            }
          }

          .doctor-languages {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;

            .language-icon {
              font-size: 12px;
              margin-right: 4px;
            }
          }
        }
      }
    }

    // Doctor Description
    .doctor-description {
      color: rgba(255, 255, 255, 1);
      font-size: 15px;
      line-height: 1.5;
      margin-bottom: 10px;
      font-weight: 300;

      align-items: center;
    }

    /* Appointment Tabs - Matching Book Appointment Styling */
    .appointment-tabs {
      padding: 0px 16px 0;
      margin-bottom: 20px;
      
      ion-segment {
        --background: transparent !important;
        border-radius: 0 !important;
        padding: 0 !important;
        background: transparent !important;

        ion-segment-button {
          --background: transparent !important;
          --background-checked: rgba(255, 255, 255, 0.1) !important;
          --color: rgba(255, 255, 255, 0.7) !important;
          --color-checked: #ffffff !important;
          --border-radius: 12px !important;
          --padding-top: 12px !important;
          --padding-bottom: 12px !important;
          font-weight: 400 !important;
          font-size: 16px !important;
          border-radius: 12px !important;
          margin: 0 !important;
          background: transparent !important;
          font-family: 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

          ion-label {
            margin: 0 !important;
            font-weight: 500 !important;
            font-size: 16px !important;
          }
        }
      }
    }

    // Date Selection
    .date-selection {
      margin-bottom: 24px;

      .date-scroll {
        display: flex;
        gap: 5px;
        overflow-x: auto;
        padding: 0 0 8px 0;

        &::-webkit-scrollbar {
          display: none;
        }

        .date-item {
          min-width: 48px;
          height: 72px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;

          &.selected {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.4);
          }

          .day {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .date {
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    // Location Info
    .location-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .location-name {
        color: #ffffff;
        font-size: 18px;
        font-weight: 500;
      }

      .location-distance {
        display: flex;
        align-items: center;
        color: rgba(255, 255, 255, 1);
        font-size: 12px;
        background-color: rgba($color: white, $alpha: 0.1);
        border-radius: 32px;
        padding-top: 9px;
        padding-left: 12px;
        padding-right: 12px;
        padding-bottom: 7px;

        .location-icon {
          font-size: 14px;
          margin-right: 4px;
          border-color: white;
          align-items: top;
        }
      }
    }

    // Time Slots Section
    .time-slots-section {
      .time-period {
        margin-bottom: 24px;

        .period-title {
          color: #ffffff;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 12px;
        }

        .time-slots {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          justify-content: flex-start;

          .time-slot {
            // Base styling from Figma - Segment Toggle Items (ALL unselected slots)
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 8px 16px;
            gap: 8px;

            // Calculate width for 3 per row with gaps - using smaller gap
            flex: 1;
            max-width: calc((100% - 16px) / 3); // 3 slots per row with 8px gaps
            min-width: calc((100% - 16px) / 3);
            height: 40px;
            border-radius: 8px;

            // Text styling - consistent for all unselected
            font-family: 'DIN Offc Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            text-align: center;

            // Default state - unselected available slots
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.50);
            border: 1px solid rgba($color: #FFFFFF, $alpha: 0.2);
            cursor: pointer;
            transition: all 0.2s ease;

            &.selected {
              // Selected state styling from Figma
              background: rgba(255, 255, 255, 0.25);
              border: 1px solid #FFFFFF;
              color: #FFFFFF;
            }

            &:not(.selected):hover {
              background: rgba(255, 255, 255, 0.2);
            }
          }
        }
      }
    }
  }

  // Book Appointment Button
  .book-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: #031934;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.2);

    .book-button {
      --background: #004AD9;
      --background-activated: #0056CC;
      --background-hover: #0056CC;
      --color: #ffffff;
      --border-radius: 8px;
      --padding-top: 16px;
      --padding-bottom: 16px;
      --text-transform: none;

      font-size: 18px;
      font-weight: 500;
      height: 52px;
      margin: 0;
      margin-bottom: 20px;
      text-transform: none;
    }
  }
}
