<ion-header class="payment-header">
  <ion-toolbar class="payment-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()">
        <ion-icon name="chevron-back" color="light"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="payment-title">Payment Confirmation</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="payment-content">
  <div class="payment-container">
    
    <!-- Appointment Date & Time -->
    <div class="appointment-datetime">
      <div class="datetime-text">{{ formattedDateTime }}</div>
    </div>

    <!-- Doctor Info Section -->
    <div class="doctor-info-section">
      <div class="specialty-label">{{ doctor.specialty }}</div>
      <div class="doctor-name-container">
        <div class="doctor-name">{{ doctor.name }}</div>
        <div class="doctor-avatar">
          <img [src]="doctor.avatar" [alt]="doctor.name">
        </div>
      </div>
    </div>

    <!-- Appointment Type -->
    <div class="appointment-details">
      <div class="detail-row">
        <span class="detail-label">Type of Appointment:</span>
      </div>
      <div class="detail-row">
        <span class="detail-value appointment-type">{{ appointmentType | titlecase }}</span>
      </div>
    </div>

    <!-- Consultation Fee -->
    <div class="consultation-fee-section">
      <div class="fee-label">Consultation Fee:</div>
      <div class="fee-amount">{{ consultationFee }}</div>
    </div>

    <!-- Total Due -->
    <div class="total-section">
      <div class="total-row">
        <span class="total-label">Total Due</span>
        <span class="total-amount">AED 99.00</span>
      </div>
    </div>

    <!-- Mode of Payment -->
    <div class="payment-mode-section">
      <div class="payment-mode-label">Mode of Payment</div>
      
      <ion-radio-group [(ngModel)]="selectedPaymentMode" (ionChange)="onPaymentModeChange($event)">
        
        <!-- Credit Card -->
        <div class="payment-option">
          <ion-item class="payment-item">
            <ion-radio slot="start" value="credit-card"></ion-radio>
            <div class="payment-option-content">
              <img src="assets/images/credit-card.svg" alt="Credit Card" class="payment-icon">
              <ion-label class="payment-label">Credit Card</ion-label>
            </div>
          </ion-item>
        </div>

        <!-- Apple Pay -->
        <div class="payment-option">
          <ion-item class="payment-item">
            <ion-radio slot="start" value="apple-pay"></ion-radio>
            <div class="payment-option-content">
              <img src="assets/images/apple-pay 1.svg" alt="Apple Pay" class="payment-icon">
              <ion-label class="payment-label">Apple Pay</ion-label>
            </div>
          </ion-item>
        </div>

        <!-- Samsung Pay -->
        <div class="payment-option">
          <ion-item class="payment-item">
            <ion-radio slot="start" value="samsung-pay"></ion-radio>
            <div class="payment-option-content">
              <img src="assets/images/samsung-pay.svg" alt="Samsung Pay" class="payment-icon">
              <ion-label class="payment-label">Samsung Pay</ion-label>
            </div>
          </ion-item>
        </div>

      </ion-radio-group>
    </div>

  </div>

  <!-- Next Button -->
  <div class="next-button-container">
    <ion-button 
      expand="block" 
      class="next-button"
      (click)="proceedToNext()">
      Next
    </ion-button>
  </div>
</ion-content>
