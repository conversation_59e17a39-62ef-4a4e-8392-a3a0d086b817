import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { URLS } from '../constants/index';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  constructor(private httpClient: HttpClient) {}

  verifyPayment(details: any): Observable<any> {
    return this.httpClient.post<any>(URLS.PAYMENT_VERIFICATION, details);
  }
}
