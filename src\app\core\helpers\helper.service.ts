import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  BehaviorSubject,
  catchError,
  Observable,
  Subject,
  throwError,
} from 'rxjs';
import { URLS } from '../constants';

@Injectable({
  providedIn: 'root', 
})
export class HelperService {
  constructor(private http: HttpClient) {}
  public header = new BehaviorSubject<any>(null);
  public upload = new BehaviorSubject<any>(null);
  public uploadPres = new BehaviorSubject<any>(null);
  private userExist = new BehaviorSubject<boolean | null>(null);
  public userExists$ = this.userExist.asObservable();
  private channelSubject = new BehaviorSubject<string>('');
  public channelValue$ = this.channelSubject.asObservable();

  getToken() {
    const obj = {
      accessToken: localStorage.getItem('accessToken'),
      refreshToken: localStorage.getItem('refreshToken'),
    };
    return obj;
  }
  getNewAccessToken() {
    const userId: any = localStorage.getItem('userID');

    return this.http
      .post(URLS.REFRESH_TOKEN, {
        accessToken: `${this.getAccessToken()}`,
        refreshToken: `${this.getRefreshToken()}`,
      })
      .pipe(
        catchError(error => {
          // Handle the error here
          console.error('Error refreshing access token:', error);

          // You can throw a custom error or return a default value as needed
          return throwError('Error refreshing access token');
        })
      );
  }
  getAccessToken(): string | null {
    const access_token = localStorage.getItem('accessToken');
    return access_token;
  }
  getRefreshToken(): string | null {
    const refresh_token = localStorage.getItem('refreshToken');
    return refresh_token;
  }
  setToken(accessToken: string, refreshToken: string): void {
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }
  getConfig():Observable<any> {
    return this.http.get<any>(URLS.CONFIG);
  }
  getChannelValue(): any {
    return this.channelSubject.getValue();
  }
  setChannelValue(value: any): void {
    this.channelSubject.next(value);
  }
  setUserExists(value: boolean): void {
    this.userExist.next(value);
  }
}
