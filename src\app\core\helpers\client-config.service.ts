import { Injectable, NgZone } from '@angular/core';
import {
  BehaviorSubject,
  catchError,
  map,
  Observable,
  of,
  Subject,
  throwError,
} from 'rxjs';
import {
  ClientConfig,
  ClientConfigParsed,
  ClientConfigRaw,
  MenuItem,
} from '../models/client-config.model';
import { CLIENT_CONFIGS } from '../constants/client-config.constant';
import { HttpClient } from '@angular/common/http';
import { URLS } from '../constants';

@Injectable({
  providedIn: 'root',
})
export class ClientConfigService {
  private clientConfigs = CLIENT_CONFIGS;
  private currentConfig$ = new BehaviorSubject<ClientConfig | null>(null);
  private rawConfig: ClientConfigRaw | null = null;
  private parsedConfig: ClientConfigParsed | null = null;
  private menuItemsSubject = new BehaviorSubject<MenuItem[]>([]);
menuItems$ = this.menuItemsSubject.asObservable();

  constructor(private http: HttpClient) {}

  loadConfig(config: ClientConfigRaw) {
    console.log('this.configggggg--->', config);

    this.rawConfig = config;
    console.log('--this.row', this.rawConfig);

    this.parsedConfig = this.transformConfig(config);
    console.log('parseConfig', this.parsedConfig);
    return this.parsedConfig;
  }
  private transformConfig(config: ClientConfigRaw): ClientConfigParsed {
    const mappedProps = config?.props?.reduce(
      (acc, item) => {
        acc[item.type] = item.payload;
        return acc;
      },
      {} as { [key: string]: string | boolean }
    );

    return {
      clientId: config.clientId,
      token: config.token,
      props: mappedProps,
    };
  }
  getParsedConfig(): ClientConfigParsed | null {
    return this.parsedConfig;
  }
  getProp(key: string): string | boolean | undefined {
    return this.parsedConfig?.props[key];
  }
  preloadClientConfig(clientId: string,darkMode:any): ClientConfig | null {
    const matched = this.clientConfigs.find(c => c.id === clientId) || null;
    if (matched) {
      console.log('darkmode inservice',darkMode);
      
      if (darkMode !== undefined) {
        matched.darkMode = darkMode === '1' ? true : false;
      }
  
      this.currentConfig$.next(matched);
    } else {
      console.warn('Invalid clientId:', clientId);
    }
    return matched;
  }
  setClientId(clientId: string): Observable<ClientConfig> {
    const matched = this.clientConfigs.find(c => c.id === clientId);
    if (!matched) {
      console.warn('Client ID not found:', clientId);
      return throwError(() => new Error('Client config not found'));
    }

    return this.fetchMenuItems().pipe(
      catchError(err => {
        console.warn('⚠️ Fetch menu items failed:', err);
        return of([]);
      }),
      map((menu: MenuItem[]) => {
        const menuItems: any = menu;
        matched.menuItems = menuItems.data;
console.log('__--__-_-__',menuItems?.data);

        this.menuItemsSubject.next(menuItems?.data ?? []);
        this.currentConfig$.next(matched);
        return matched;
      })
    );
  }
  private fetchMenuItems(): Observable<MenuItem[]> {
    return this.http.get<MenuItem[]>(URLS.MENU_ITEMS);
  }
  updateConfig(partial: Partial<ClientConfig>) {
    const current = this.currentConfig$.getValue();
    if (current) {
      const updated = { ...current, ...partial };
      this.currentConfig$.next(updated);
    }
  }
  getClientConfig(): Observable<ClientConfig | null> {
    return this.currentConfig$.asObservable();
  }
  getClientCodeSnapshot(): number | null {
    return this.currentConfig$.getValue()?.code || null;
  }
  getMenuItemsSnapshot(): MenuItem[] {
    return this.currentConfig$.getValue()?.menuItems || [];
  }
  getClientdarkModeSnapshot(): boolean | null {
    return this.currentConfig$.getValue()?.darkMode || null;
  }
  getClientLanguageSnapshot(): string | null {
    return this.currentConfig$.getValue()?.language || null;
  }
  getClientIDSnapshot(): string | null {
    return this.currentConfig$.getValue()?.id || null;
  }
  getClientSnapshot(): string | null {
    return this.currentConfig$.getValue()?.vendorName || null;
  }
}
