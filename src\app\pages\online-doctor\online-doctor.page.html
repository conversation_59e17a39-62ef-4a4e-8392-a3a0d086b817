<ion-header class="online-doctor-header">
  <ion-toolbar class="online-doctor-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()">
        <ion-icon name="chevron-back" color="light"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="online-doctor-title">Online Doctor</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="online-doctor-content">
  <div class="online-doctor-container">
    
    <!-- Search Bar -->
    <div class="search-section">
      <div class="search-container">
        <ion-searchbar
          [(ngModel)]="searchQuery"
          (ionInput)="onSearchInput($event)"
          placeholder="Search for doctors, specialities and more"
          class="custom-searchbar">
        </ion-searchbar>
      </div>
    </div>

    <!-- Consultation Card -->
    <div class="consultation-card-section">
      <ion-card class="consultation-card">
        <ion-card-content class="consultation-card-content">
          <div class="consultation-text">
            <h2>Consult with your trusted doctors, online or in-person</h2>
          </div>
          <div class="consultation-action">
            <ion-button
              class="book-appointment-btn"
              (click)="bookAppointment()">
              Book appointment
              <ion-icon name="chevron-forward" slot="end"></ion-icon>
            </ion-button>
          </div>
          <div class="consultation-icon">
            <img src="assets/images/consultationbox.svg" alt="Consultation" class="consultation-img">
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Upcoming Appointments -->
    <div class="upcoming-section">
      <h3 class="section-title">Upcoming appointments</h3>
      
      <div class="appointment-card" (click)="viewUpcomingAppointment()">
        <div class="appointment-time-status">
          <span class="time">{{ upcomingAppointment.time }}</span>
          <ion-badge class="status-badge confirmed">{{ upcomingAppointment.status }}</ion-badge>
        </div>
        <div class="appointment-divider"></div>

        <div class="doctor-card-content">
          <div class="doctor-main-info">
            <div class="doctor-left-section">
              <div class="doctor-avatar">
                <img [src]="upcomingAppointment.doctor.avatar" [alt]="upcomingAppointment.doctor.name">
              </div>
              <div class="doctor-details">
                <div class="doctor-name-specialty">
                  <div class="doctor-name-rating">
                    <h3 class="doctor-name">{{ upcomingAppointment.doctor.name }}</h3>
                    <div class="doctor-rating">
                      <ion-icon name="star"></ion-icon>
                      <span class="rating-text">{{ upcomingAppointment.doctor.rating }} ({{ upcomingAppointment.doctor.ratingCount }})</span>
                    </div>
                  </div>
                  <p class="doctor-specialty">{{ upcomingAppointment.doctor.specialty }}</p>
                </div>
                <div class="appointment-info">
                  <div class="appointment-date">
                    <ion-icon name="calendar" class="calendar-icon"></ion-icon>
                    <span>{{ upcomingAppointment.date }}</span>
                  </div>
                  <div class="appointment-type">
                    <ion-icon name="laptop" class="laptop-icon"></ion-icon>
                    <span>{{ upcomingAppointment.type }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="doctor-right-section">
              <ion-icon name="chevron-forward" class="arrow-icon"></ion-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Explore Services -->
    <div class="explore-section">
      <h3 class="section-title">Explore Pura Online Doctor</h3>
      
      <div class="services-grid">
        <div
          *ngFor="let service of exploreServices"
          class="service-item"
          (click)="onServiceClick(service)">
          <div class="service-icon">
            <ion-icon *ngIf="!service.isSvgIcon" [name]="service.icon" class="icon"></ion-icon>
            <img *ngIf="service.isSvgIcon" [src]="'assets/images/' + service.icon" [alt]="service.title" class="icon svg-icon">
          </div>
          <span class="service-title">{{ service.title }}</span>
        </div>
      </div>
    </div>

  </div>
</ion-content>
